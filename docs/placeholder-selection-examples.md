# Practical Examples: Placeholder Selection in Action

## Overview

This guide provides real-world examples of how each event type works in practice. Each example includes the configuration, sample content, and the resulting ad placement.

## EVERY_POSITION Examples

### Example 1: Fixed Ad Positions in News Article

**Scenario**: You want ads at specific positions in every news article - after the headline, in the middle, and at the end.

**Configuration**:
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1, 3, "last"]
    }
  }
}
```

**Sample Content Structure**:
```
1. [Placeholder] ← Ad will be placed here
2. Headline
3. [Placeholder] ← Ad will be placed here  
4. First paragraph
5. [Placeholder] ← Ad will be placed here (last)
6. Second paragraph
```

**Result**: Ads appear at positions 1, 3, and the last placeholder position.

### Example 2: Ads Relative to Content Elements

**Scenario**: Place ads below the 2nd paragraph and above the 5th paragraph.

**Configuration**:
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "elementsPositions": [
        [2, "under"],
        [5, "above"]
      ]
    }
  }
}
```

**Sample Content Structure**:
```
1. Headline
2. First paragraph
3. [Placeholder] ← Ad placed here (under 2nd element)
4. Second paragraph
5. Third paragraph
6. [Placeholder] ← Ad placed here (above 5th element)
7. Fourth paragraph
8. Fifth paragraph
```

**Result**: Two ads placed relative to specific content elements.

### Example 3: Mobile-Optimized Positioning

**Scenario**: Different ad positions for mobile vs desktop users.

**Configuration** (Mobile):
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [2, 4],
      "countBackwards": false
    }
  }
}
```

**Configuration** (Desktop):
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1, 3, 5],
      "countBackwards": false
    }
  }
}
```

**Result**: Mobile gets 2 ads, desktop gets 3 ads at different positions.

## EVERY_X Examples

### Example 4: Regular Ad Distribution in Long Articles

**Scenario**: Place an ad after every 3 paragraphs in a long blog post.

**Configuration**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 3,
      "position": "under"
    }
  }
}
```

**Sample Content Flow**:
```
1. Paragraph 1
2. Paragraph 2  
3. Paragraph 3
4. [AD PLACEMENT] ← After 3rd paragraph
5. Paragraph 4
6. Paragraph 5
7. Paragraph 6
8. [AD PLACEMENT] ← After 6th paragraph
9. Paragraph 7
10. Paragraph 8
11. Paragraph 9
12. [AD PLACEMENT] ← After 9th paragraph
```

**Result**: Consistent ad spacing throughout the content.

### Example 5: Targeted Content Type Distribution

**Scenario**: Place ads every 2 images, but only count actual images (ignore other content).

**Configuration**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 2,
      "element": ["image"],
      "position": "under"
    }
  }
}
```

**Sample Content Structure**:
```
1. Paragraph
2. Image 1
3. Paragraph
4. Image 2
5. [AD PLACEMENT] ← After 2nd image
6. Paragraph
7. Image 3
8. Video (ignored)
9. Image 4
10. [AD PLACEMENT] ← After 4th image (2nd cycle)
```

**Result**: Ads appear only relative to images, every 2nd image.

### Example 6: Limited Ad Frequency

**Scenario**: Place ads every 4 paragraphs, but never more than 2 ads total.

**Configuration**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 4,
      "max": 2,
      "position": "under"
    }
  }
}
```

**Result**: Even in very long content, only 2 ads will be placed maximum.

## NEAR_TO Examples

### Example 7: Ad Near Hero Image

**Scenario**: Place an ad immediately after the main hero image in articles.

**Configuration**:
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": "hero-image",
      "position": "under"
    }
  }
}
```

**Sample Content Structure**:
```
1. Article headline
2. Hero image
3. [AD PLACEMENT] ← Placed here
4. Article introduction
5. First paragraph
```

**Result**: Single ad placed directly after the hero image.

### Example 8: Contextual Video Advertising

**Scenario**: Place an ad above the first video in entertainment articles.

**Configuration**:
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": "video",
      "position": "above"
    }
  }
}
```

**Sample Content Structure**:
```
1. Article text
2. More text
3. [AD PLACEMENT] ← Placed here
4. Video content
5. Continuation of article
```

**Result**: Ad appears right before the video, creating contextual relevance.

### Example 9: Multi-Element Targeting

**Scenario**: Place ad near whichever visual element appears first (image, video, or infographic).

**Configuration**:
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": ["image", "video", "infographic"],
      "position": "under"
    }
  }
}
```

**Result**: System finds the first visual element and places ad after it.

## NEAR_TO_INDEXES Examples

### Example 10: Gallery Advertising Strategy

**Scenario**: In photo galleries, place ads after the 3rd and 6th images.

**Configuration**:
```json
{
  "type": "nearToIndexes",
  "params": {
    "placeholder": {
      "element": "image",
      "elementsIndexes": [3, 6],
      "position": "under"
    }
  }
}
```

**Sample Gallery Structure**:
```
1. Image 1
2. Image 2
3. Image 3
4. [AD PLACEMENT] ← After 3rd image
5. Image 4
6. Image 5
7. Image 6
8. [AD PLACEMENT] ← After 6th image
9. Image 7
10. Image 8
```

**Result**: Strategic ad placement within the gallery flow.

### Example 11: End-Weighted Ad Strategy

**Scenario**: Place ads near the last 2 videos in a video-heavy article.

**Configuration**:
```json
{
  "type": "nearToIndexes",
  "params": {
    "placeholder": {
      "element": "video",
      "elementsIndexes": [1, 2],
      "countBackwards": true,
      "position": "above"
    }
  }
}
```

**Sample Content** (5 videos total):
```
1. Video 1
2. Video 2
3. Video 3
4. [AD PLACEMENT] ← Before 2nd-to-last video (Video 4)
5. Video 4
6. [AD PLACEMENT] ← Before last video (Video 5)
7. Video 5
```

**Result**: Ads placed strategically near the end of the content.

## Complex Scenarios

### Example 12: Multi-Strategy Campaign

**Scenario**: Combine multiple event types for comprehensive ad coverage.

**Strategy 1 - Primary Placement**:
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1]
    }
  }
}
```

**Strategy 2 - Content-Based Placement**:
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": "image",
      "position": "under"
    }
  }
}
```

**Strategy 3 - Distribution Placement**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 5,
      "max": 2
    }
  }
}
```

**Result**: Comprehensive ad coverage with guaranteed placement, contextual relevance, and distribution control.

### Example 13: Responsive Design Considerations

**Scenario**: Different strategies based on device type and content length.

**Mobile Configuration** (shorter attention span):
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 2,
      "max": 3,
      "position": "under"
    }
  }
}
```

**Desktop Configuration** (longer content consumption):
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1, 3, 5, "last"]
    }
  }
}
```

**Result**: Optimized user experience across different devices.

## Parameter Combination Effects

### Example 14: Advanced EVERY_X Configuration

**Configuration**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 3,
      "element": ["paragraph", "quote"],
      "position": "under",
      "max": 4,
      "ommitLast": true,
      "exclude": true
    }
  }
}
```

**Behavior**:
- Counts only paragraphs and quotes
- Places ads every 3rd qualifying element
- Maximum 4 ads total
- Removes last placeholder if it exists
- Uses exclusion logic for better placement

**Result**: Sophisticated ad placement with multiple constraints and optimizations.

## Best Practice Examples

### Example 15: User Experience Optimization

**Good Practice**:
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 4,
      "max": 3,
      "position": "under"
    }
  }
}
```

**Why it works**:
- Reasonable spacing (every 4 elements)
- Limited frequency (max 3 ads)
- Non-intrusive positioning (under content)

### Example 16: Content-Aware Placement

**Good Practice**:
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": ["product-image", "review-section"],
      "position": "under"
    }
  }
}
```

**Why it works**:
- Contextually relevant (near product content)
- Flexible targeting (multiple element types)
- Natural flow (after relevant content)

These examples demonstrate how different event types can be configured for various use cases, from simple fixed positioning to complex contextual placement strategies.
