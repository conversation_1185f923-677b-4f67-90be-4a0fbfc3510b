# Complete Guide to Placeholder Selection

## Table of Contents

1. [Introduction](#introduction)
2. [How Placeholder Selection Works](#how-placeholder-selection-works)
3. [Visual Flow Diagram](#visual-flow-diagram)
4. [Event Types Overview](#event-types-overview)
5. [Decision Points and Branches](#decision-points-and-branches)
6. [Practical Examples](#practical-examples)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)
9. [Advanced Topics](#advanced-topics)

## Introduction

The placeholder selection system determines where advertisements appear within your content. It uses sophisticated logic to choose the best positions for ads based on your configuration, content structure, and user context.

This guide explains how the system works from a business perspective, helping you understand and control ad placement without needing technical implementation details.

### Key Concepts

- **Placeholder**: A designated slot where an advertisement can be placed
- **Event**: A configuration that defines how and where to place ads
- **Fact**: A section of content (like an article, gallery, or content block)
- **Element**: Individual pieces of content (paragraphs, images, videos, etc.)

## How Placeholder Selection Works

### The Basic Process

1. **Content Analysis**: The system examines your content structure
2. **Configuration Application**: Your placement rules are applied
3. **Position Calculation**: Specific ad positions are determined
4. **Validation**: Positions are checked for validity
5. **Result Generation**: Final ad placements are returned

### Two Main Pathways

#### Pathway 1: Explicit Content Targeting
When you specify exactly which content section to use:
- **Faster processing**
- **Predictable results**
- **Direct control**

#### Pathway 2: Automatic Content Discovery
When the system finds suitable content automatically:
- **More flexible**
- **Adapts to content variations**
- **May be less predictable**

## Visual Flow Diagram

```mermaid
flowchart TD
    A[Start: Process Event] --> B{Does event have containing fact?}
    
    B -->|Yes| C[Use getPlaceholdersByEventType]
    B -->|No| D[Get almanac fact names]
    
    D --> E[Extract placeholder elements from event]
    E --> F[Find containing fact using getContainingFact]
    F --> G{Containing fact found?}
    
    G -->|No| H[Return empty array]
    G -->|Yes| I[Use getPlaceholdersByEventTypeForNotProvidedContainingFact]
    
    C --> J{Event Type?}
    I --> K{Event Type for No Containing Fact?}
    
    K -->|NEAR_TO| L[getNearToElementPlaceholder]
    K -->|NEAR_TO_INDEXES| M[getPlaceholdersNearIndexes]
    K -->|Other| N[Log invalid invocation, return empty]
    
    J -->|EVERY_POSITION| O[getEveryPositionPlaceholder]
    J -->|EVERY_X| P[getEveryXPlaceholder]
    J -->|NEAR_TO| Q[getNearToElementPlaceholder]
    J -->|NEAR_TO_INDEXES| R[getPlaceholdersNearIndexes]
    
    O --> O1{Has placeholderPositions?}
    O1 -->|Yes| O2[Filter placeholders from fact]
    O1 -->|No| O3{Has elementsPositions?}
    
    O2 --> O4{Has placeholderType filter?}
    O4 -->|Yes| O5[Filter by placeholder type]
    O4 -->|No| O6{Count backwards?}
    O5 --> O6
    
    O6 -->|Yes| O7[Reverse placeholder array]
    O6 -->|No| O8[Keep original order]
    O7 --> O9[Apply position filters]
    O8 --> O9
    
    O9 --> END1[Return filtered placeholders]
    
    O3 -->|Yes| O13[Process elementsPositions]
    O3 -->|No| O14[Log invalid event, return empty]
    
    P --> P1{Has 'every' parameter?}
    P1 -->|No| P2[Log invalid event, return empty]
    P1 -->|Yes| P3[Apply every-X logic]
    P3 --> END3[Return every-X placeholders]
    
    Q --> Q1[Find target element]
    L --> Q1
    Q1 --> Q4{Element found?}
    Q4 -->|No| Q5[Return empty array]
    Q4 -->|Yes| Q6[Place ad near element]
    Q6 --> END4[Return near-to placeholder]
    
    R --> R1{Has elementsIndexes?}
    M --> R1
    R1 -->|No| R2[Log invalid event, return empty]
    R1 -->|Yes| R3[Process indexed elements]
    R3 --> END5[Return indexed placeholders]
    
    END1 --> Z[End: Return Selected Placeholders]
    END3 --> Z
    END4 --> Z
    END5 --> Z
    H --> Z
    N --> Z
    O14 --> Z
    P2 --> Z
    Q5 --> Z
    R2 --> Z
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style H fill:#ffcdd2
    style N fill:#ffcdd2
    style O14 fill:#ffcdd2
    style P2 fill:#ffcdd2
    style Q5 fill:#ffcdd2
    style R2 fill:#ffcdd2
```

## Event Types Overview

### EVERY_POSITION
**Purpose**: Place ads at specific, predetermined positions
**Best for**: Consistent placement across similar content
**Key feature**: Precise control over ad locations

**Quick Example**: "Place ads at positions 1, 3, and 5"

### EVERY_X  
**Purpose**: Distribute ads at regular intervals
**Best for**: Long content with variable length
**Key feature**: Automatic spacing adjustment

**Quick Example**: "Place an ad every 4 paragraphs"

### NEAR_TO
**Purpose**: Place ads near specific content types
**Best for**: Contextual advertising
**Key feature**: Content-aware placement

**Quick Example**: "Place an ad after the first image"

### NEAR_TO_INDEXES
**Purpose**: Place ads near multiple specific occurrences
**Best for**: Complex contextual strategies
**Key feature**: Multiple targeted placements

**Quick Example**: "Place ads after the 2nd and 4th images"

## Decision Points and Branches

The system makes several key decisions that affect ad placement:

### 1. Content Section Discovery
- **With explicit targeting**: Uses specified content directly
- **With automatic discovery**: Searches for suitable content

### 2. Event Type Processing
- **EVERY_POSITION**: Chooses between position-based or element-relative placement
- **EVERY_X**: Applies interval logic with optional filtering
- **NEAR_TO**: Finds first matching element
- **NEAR_TO_INDEXES**: Processes multiple specific occurrences

### 3. Parameter Application
- **Filtering**: Applies type, position, and other filters
- **Ordering**: Handles forward/backward counting
- **Limits**: Enforces maximum ad counts
- **Validation**: Ensures valid placements

### 4. Error Handling
- **Missing elements**: Returns empty results gracefully
- **Invalid configurations**: Logs warnings and uses safe defaults
- **Edge cases**: Handles boundary conditions appropriately

## Practical Examples

### Simple Fixed Placement
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1, 3, "last"]
    }
  }
}
```
**Result**: Ads at 1st, 3rd, and last positions

### Regular Distribution
```json
{
  "type": "everyX", 
  "params": {
    "placeholder": {
      "every": 4,
      "max": 3
    }
  }
}
```
**Result**: Up to 3 ads, placed every 4th element

### Contextual Placement
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": "image",
      "position": "under"
    }
  }
}
```
**Result**: One ad placed after the first image

## Best Practices

### Content Strategy
1. **Know Your Content**: Understand your content structure before configuring
2. **Test Configurations**: Try different settings to find optimal placement
3. **Consider User Experience**: Balance ad revenue with user satisfaction
4. **Monitor Performance**: Track how different placements perform

### Technical Configuration
1. **Start Simple**: Begin with basic configurations, add complexity gradually
2. **Use Appropriate Event Types**: Match event type to your placement goals
3. **Set Reasonable Limits**: Avoid overwhelming users with too many ads
4. **Plan for Edge Cases**: Consider what happens with unusual content

### Device Optimization
1. **Mobile Considerations**: Use fewer, better-placed ads on mobile
2. **Desktop Strategies**: Take advantage of larger screens
3. **Responsive Design**: Adapt placement strategies to screen size

## Troubleshooting

### Common Issues

#### No Ads Appearing
**Possible Causes**:
- Target elements don't exist in content
- Invalid configuration parameters
- No suitable placeholder slots available

**Solutions**:
- Verify element names match content
- Check configuration syntax
- Ensure content has placeholder slots

#### Unexpected Ad Placement
**Possible Causes**:
- Automatic content discovery choosing different section
- Parameter interactions causing unexpected behavior
- Device-specific optimizations affecting placement

**Solutions**:
- Use explicit content targeting
- Simplify configuration to isolate issues
- Test across different devices

#### Too Many/Few Ads
**Possible Causes**:
- Incorrect interval settings
- Missing or incorrect max limits
- Content length variations

**Solutions**:
- Adjust interval parameters
- Set appropriate maximum limits
- Test with various content lengths

## Advanced Topics

### Multiple Event Strategies
Combine different event types for comprehensive coverage:
- Primary placement with EVERY_POSITION
- Distribution with EVERY_X
- Contextual enhancement with NEAR_TO

### Device-Specific Optimization
Configure different strategies for different devices:
- Mobile: Fewer, strategic placements
- Desktop: More comprehensive coverage
- Tablet: Balanced approach

### Performance Considerations
- Explicit content targeting is faster than automatic discovery
- Simple configurations process more quickly
- Caching improves repeated operations

### Content Adaptation
The system adapts to:
- Variable content lengths
- Different content structures
- Missing or unexpected elements
- Device capabilities and constraints

## Related Documentation

- [EVERY_POSITION and EVERY_X Details](placeholder-selection-every-position-every-x.md)
- [NEAR_TO and NEAR_TO_INDEXES Details](placeholder-selection-near-to-near-to-indexes.md)
- [Practical Examples Collection](placeholder-selection-examples.md)
- [Decision Branches Deep Dive](placeholder-selection-decision-branches.md)

## Support and Further Information

For additional help with placeholder selection:
1. Review the practical examples for similar use cases
2. Check the decision branches guide for understanding system behavior
3. Test configurations in a development environment
4. Monitor performance metrics to optimize placement strategies

This system provides powerful, flexible ad placement capabilities while maintaining user experience and system performance. Understanding these concepts will help you create effective advertising strategies that serve both business goals and user needs.
