# Decision Branches in Placeholder Selection

## Overview

The placeholder selection system makes decisions at multiple points to determine where ads should be placed. Understanding these decision points helps you predict and control ad placement behavior. This guide explains each decision branch, what triggers it, and what happens as a result.

## Primary Decision: Containing Fact Availability

### Decision Point 1: Does the event have a containing fact?

**What this means**: The system first checks whether you've explicitly specified which content section (fact) should be used for ad placement.

#### Branch A: Containing Fact Provided ✅
**Trigger**: You've specified exactly which content section to use
**What happens**: 
- System uses the `getPlaceholdersByEventType` method
- Processes the specified content section directly
- Applies your event type rules to that specific content

**Business Impact**: 
- More predictable ad placement
- Faster processing
- Direct control over which content gets ads

**When this occurs**:
- You've configured specific content sections
- Working with known, structured content
- Using targeted placement strategies

#### Branch B: No Containing Fact Provided ❓
**Trigger**: You haven't specified which content section to use
**What happens**:
1. System searches through all available content sections
2. Looks for sections containing your target elements
3. Uses the first matching section found
4. If found, processes with `getPlaceholdersByEventTypeForNotProvidedContainingFact`
5. If not found, returns no ads

**Business Impact**:
- More flexible but less predictable
- Automatic content discovery
- Potential for unexpected placement

**When this occurs**:
- Working with variable content structures
- Using flexible, adaptive placement strategies
- Content structure isn't predetermined

## Event Type Decision Branches

### Decision Point 2: Which event type processing method?

After determining the content section, the system chooses how to process ad placement based on your event type.

#### Branch 2A: EVERY_POSITION Processing
**Trigger**: Event type is "everyPosition"
**What happens**: System uses position-based placement logic

**Sub-decisions within EVERY_POSITION**:

##### Sub-Branch 2A1: Placeholder Positions vs Elements Positions
**Decision**: Does the configuration specify `placeholderPositions` or `elementsPositions`?

**If placeholderPositions** 📍:
- Counts existing placeholder slots
- Selects slots at specified positions
- Applies filters (type, backwards counting)
- Handles special cases (like "last" position)

**If elementsPositions** 🎯:
- Counts content elements (not placeholders)
- Finds placeholders adjacent to specified elements
- Considers position (above/under)
- Applies variant weighting if enabled

**Business Impact**: 
- placeholderPositions: Direct control over ad slots
- elementsPositions: Content-relative placement

#### Branch 2B: EVERY_X Processing
**Trigger**: Event type is "everyX"
**What happens**: System uses interval-based placement logic

**Sub-decisions within EVERY_X**:

##### Sub-Branch 2B1: Element Filtering
**Decision**: Are specific element types specified?
- **If yes**: Only counts specified element types for intervals
- **If no**: Counts all non-placeholder elements

##### Sub-Branch 2B2: Exclusion Logic
**Decision**: Is exclude parameter enabled with position preference?
- **If yes**: Uses sophisticated exclusion logic with position fallback
- **If no**: Uses standard interval counting

**Business Impact**:
- Element filtering: Targeted interval counting
- Exclusion logic: More sophisticated placement control

#### Branch 2C: NEAR_TO Processing
**Trigger**: Event type is "nearTo"
**What happens**: System finds first matching element and places ad nearby

**Sub-decisions within NEAR_TO**:

##### Sub-Branch 2C1: Element Discovery
**Decision**: Can the target element be found?
- **If found**: Proceeds to position determination
- **If not found**: Returns no ads

##### Sub-Branch 2C2: Position Determination
**Decision**: Where should the ad be placed relative to the element?
- **Above**: Places ad in placeholder slot before the element
- **Under**: Places ad in placeholder slot after the element
- **Default**: Uses "above" if not specified

**Business Impact**:
- Element discovery: Determines if contextual placement is possible
- Position choice: Controls user experience flow

#### Branch 2D: NEAR_TO_INDEXES Processing
**Trigger**: Event type is "nearToIndexes"
**What happens**: System finds multiple occurrences and places ads near specified ones

**Sub-decisions within NEAR_TO_INDEXES**:

##### Sub-Branch 2D1: Index Validation
**Decision**: Are the specified indexes valid?
- **If valid**: Processes each specified index
- **If invalid**: Skips invalid indexes, processes valid ones

##### Sub-Branch 2D2: Counting Direction
**Decision**: Should counting be forwards or backwards?
- **Forwards**: 1st element is first in content
- **Backwards**: 1st element is last in content

**Business Impact**:
- Index validation: Prevents errors with dynamic content
- Counting direction: Enables end-weighted strategies

## Special Processing Branches

### Decision Point 3: Multiple Facts Processing

**Trigger**: Configuration includes `containsSomeType` parameter
**What happens**: System processes multiple content sections and combines results

#### Branch 3A: Multiple Facts Found
**What happens**:
1. Processes each qualifying content section separately
2. Applies event type logic to each section
3. Combines all results into single ad list
4. Returns flattened result

**Business Impact**: Enables comprehensive ad coverage across multiple content sections

#### Branch 3B: No Qualifying Facts
**What happens**: Returns empty result

### Decision Point 4: Fact Acceptance Logic

**Trigger**: Event type is NEAR_TO_INDEXES with "any" fact specification
**What happens**: System accepts any available content section instead of specific ones

**Business Impact**: Maximum flexibility for dynamic content structures

## Error Handling Branches

### Decision Point 5: Invalid Configuration Detection

The system includes multiple error detection branches:

#### Branch 5A: Missing Required Parameters
**Triggers**:
- EVERY_X without "every" parameter
- NEAR_TO_INDEXES without "elementsIndexes"
- Invalid event type

**What happens**: 
- Logs appropriate warning/error
- Returns empty ad list
- Prevents system crashes

#### Branch 5B: Empty Content Handling
**Triggers**:
- No content available
- No placeholders in content
- No matching elements found

**What happens**:
- Gracefully returns empty results
- Logs informational messages
- Maintains system stability

## Device Type Considerations

### Decision Point 6: Device-Specific Processing

**Trigger**: Device type affects placeholder selection (primarily in EVERY_POSITION)
**What happens**: 
- Variant weighting calculations consider device type
- Different processing paths for mobile vs desktop
- Optimized ad placement for screen size

**Business Impact**: 
- Better user experience across devices
- Device-optimized ad performance
- Responsive ad placement strategies

## Performance Optimization Branches

### Decision Point 7: Caching and Optimization

**Trigger**: System detects opportunities for optimization
**What happens**:
- Caches frequently accessed content sections
- Optimizes repeated calculations
- Reduces processing overhead

**Business Impact**:
- Faster ad placement decisions
- Better system performance
- Improved user experience

## Fallback Strategies

### Decision Point 8: Graceful Degradation

**When primary strategies fail**:
1. **Element not found**: Returns empty instead of crashing
2. **Invalid positions**: Ignores invalid, processes valid ones
3. **Configuration errors**: Uses safe defaults where possible

**Business Impact**:
- System reliability
- Predictable behavior under edge conditions
- Reduced support burden

## Decision Flow Summary

```
1. Containing Fact Check
   ├── Provided → Direct Processing
   └── Not Provided → Auto-Discovery → Processing or Empty

2. Event Type Routing
   ├── EVERY_POSITION → Position Logic → Placeholder/Element Choice
   ├── EVERY_X → Interval Logic → Filtering/Exclusion Choice  
   ├── NEAR_TO → Element Search → Position Choice
   └── NEAR_TO_INDEXES → Index Validation → Direction Choice

3. Special Processing
   ├── Multiple Facts → Combine Results
   ├── Any Fact → Accept All
   └── Device Considerations → Optimize for Device

4. Error Handling
   ├── Invalid Config → Log and Return Empty
   ├── Missing Elements → Graceful Failure
   └── Edge Cases → Safe Defaults

5. Result Assembly
   └── Return Selected Placeholders
```

## Business Decision Guidelines

### When to Use Each Branch Strategy

**Use Containing Fact Specification When**:
- Content structure is predictable
- Performance is critical
- Precise control is needed

**Use Auto-Discovery When**:
- Content structure varies
- Flexibility is more important than predictability
- Working with dynamic content

**Choose Event Types Based On**:
- EVERY_POSITION: Known content structure, precise control needed
- EVERY_X: Variable content length, consistent distribution desired
- NEAR_TO: Contextual relevance is priority, simple targeting
- NEAR_TO_INDEXES: Complex contextual strategies, multiple placements

Understanding these decision branches helps you configure the system to match your business needs and predict how ads will be placed in different scenarios.
