flowchart TD
    A[Start: Process Event] --> B{Does event have containing fact?}
    
    B -->|Yes| C[Use getPlaceholdersByEventType]
    C --> J{Event Type?}

    B -->|No| D[Get almanac fact names]
    D --> E[Extract placeholder elements from event]
    E --> F[Find containing fact using getContainingFact]
    F --> G{Containing fact found?}
    
    G -->|Yes| I[Use getPlaceholdersByEventTypeForNotProvidedContainingFact]
    I --> K{Event Type for No Containing Fact?}
    
    G -->|No| REA2[Return empty array]
    
    K -->|NEAR_TO| L[getNearToElementPlaceholder]
    L --> Q1
    
    K -->|NEAR_TO_INDEXES| M[getPlaceholdersNearIndexes]
    M --> R1
    
    K -->|Other| REA2

    J -->|EVERY_POSITION| O[getEveryPositionPlaceholder]
    O --> O1{Has placeholderPositions?}

    O1 -->|Yes| O2[Filter placeholders from fact]
    O1 -->|No| O3{Has elementsPositions?}
    O3 -->|Yes| O13[Process elementsPositions]
    O3 -->|No| REA1

    O2 --> O4{Has placeholderType filter?}
    O4 -->|Yes| O5[Filter by placeholder type]
    O4 -->|No| O6{Count backwards?}
    O5 --> O6
    
    O6 -->|Yes| O7[Reverse placeholder array]
    O6 -->|No| O9
    O7 --> O9[Apply position filters]
    
    O9 --> O10{Contains 'last' position?}
    O10 -->|Yes| O11[Add last placeholder]
    O10 -->|No| O12[Filter by numeric positions]
    O11 --> O12
    O12 --> END1[Return filtered placeholders]
    
    
    O13 --> O15{Count backwards?}
    O15 -->|Yes| O16[Reverse fact array]
    O15 -->|No| O18
    O16 --> O18[Map non-placeholder indexes]    
    
    O18 --> O19{Enable variant weighting?}
    O19 -->|Yes| O20[Apply variant weights to indexes]
    O19 -->|No| O21[Use standard indexing]
    
    O20 --> O22[Find placeholders at specified positions]
    O21 --> O22
    O22 --> O23{Position is UNDER or ABOVE?}
    O23 -->|UNDER| O24[Get placeholder after element]
    O23 -->|ABOVE| O25[Get placeholder before element]
    O24 --> O26[Validate placeholder type]
    O25 --> O26
    O26 --> END2[Return positioned placeholders]


    J -->|EVERY_X| P[getEveryXPlaceholder]
    
    P --> P1{Has 'every' parameter?}
    P1 -->|No| REA1[Return empty array]
    P1 -->|Yes| P3{Fact array empty?}
    P3 -->|Yes| REA1
    P3 -->|No| P5{Omit last placeholder?}
    
    P5 -->|Yes| P6[Remove last element if placeholder]
    P5 -->|No| P7[Create extended facts with neighbors]
    P6 --> P7
    
    P7 --> P8[Map facts with above/under placeholders]
    P8 --> P9{Has element filter?}
    P9 -->|Yes| P10[Filter facts by element types]
    P9 -->|No| P11[Use all non-placeholder facts]
    
    P10 --> P12{Has exclude parameter and position?}
    P11 --> P12
    P12 -->|Yes| P13[Apply exclude logic with position preference]
    P12 -->|No| P16[Select placeholders every X elements]
    P13 --> P16
    P16 --> P17{Apply max limit?}
    P17 -->|Yes| P18[Limit to max count]
    P17 -->|No| END3[Return every-X placeholders]
    P18 --> END3
    
    J -->|NEAR_TO| Q[getNearToElementPlaceholder]
    
    Q --> Q1[Extract position and element from event]
    Q1 --> Q2[Convert element to array]
    Q2 --> Q3[Find matching element index in fact]
    Q4 -->|Yes| Q6{Position specified?}
    Q3 --> Q4{Element found?}
    Q4 -->|No| REA3[Return empty array]
    
    Q6 -->|No position| Q9[Default to ABOVE]
    Q6 -->|ABOVE| Q7[Get placeholder before element]
    Q6 -->|UNDER| Q8[Get placeholder after element]
    Q9 --> Q7
    
    Q7 --> Q10{Valid placeholder found?}
    Q8 --> Q10
    Q10 -->|Yes| END4[Return near-to placeholder]
    Q10 -->|No| REA3
    

    J -->|NEAR_TO_INDEXES| R[getPlaceholdersNearIndexes]
    
    R --> R1{Has elementsIndexes?}
    R1 -->|No| REA3
    R1 -->|Yes| R3{Count backwards?}
    
    R3 -->|Yes| R4[Reverse fact array]
    R3 -->|No| R6
    R4 --> R6[Find matching element in facts]
    
    R6 --> R7[Create indexed fact array]
    R7 --> R8[Process each element index]
    R8 --> R9{Position is ABOVE or UNDER?}
    R9 -->|ABOVE| R10[Get placeholder before indexed element]
    R9 -->|UNDER| R11[Get placeholder after indexed element]
    
    R10 --> R12{Valid placeholder found?}
    R11 --> R12
    R12 -->|Yes| R13[Add to results]
    R12 -->|No| R14[Skip this index]
    R13 --> R15{More indexes to process?}
    R14 --> R15
    R15 -->|Yes| R8
    R15 -->|No| END5[Return indexed placeholders]
    
    END1 --> Z[End: Return Selected Placeholders]
    END2 --> Z
    END3 --> Z
    END4 --> Z
    END5 --> Z
    REA3 --> Z
    REA1 --> Z
    REA2 --> Z
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style REA3 fill:#ffcdd2
    style REA1 fill:#ffcdd2
    style REA2 fill:#ffcdd2