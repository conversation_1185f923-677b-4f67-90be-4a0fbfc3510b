# NEAR_TO and NEAR_TO_INDEXES Event Types

## Overview

The NEAR_TO and NEAR_TO_INDEXES event types are specialized placement strategies that position advertisements relative to specific content elements. These are particularly useful when you want ads to appear near certain types of content, like images, videos, or specific article sections.

## NEAR_TO Event Type

### What it does
NEAR_TO finds a specific type of content element (like an image or video) and places an advertisement either directly above or below it. It's like saying "find the first photo in this article and put an ad right next to it."

### How it works
1. The system searches through your content looking for the specified element type
2. When it finds the first matching element, it identifies the placeholder slot adjacent to it
3. It places the ad in that slot, either above or below the target element

### Configuration Options

#### 1. Element (`element`) - Required
**Purpose**: Specifies what type of content to look for
**How to use**: Provide the name of the content type you want to target

**Example scenarios**:
- `element: "image"` - Looks for the first image in the content
- `element: "video"` - Looks for the first video in the content
- `element: "quote"` - Looks for the first quote block in the content
- `element: ["image", "video"]` - Looks for either an image OR video (whichever comes first)

#### 2. Position (`position`)
**Purpose**: Determines whether the ad appears above or below the target element
**Options**: 
- `"above"` - Places the ad before the target element
- `"under"` - Places the ad after the target element
**Default**: `"above"`

### Behavior Details

**Element Search Process**:
- Searches from the beginning of the content
- Stops at the first matching element found
- If multiple element types are specified, uses the first one encountered
- Returns empty result if no matching element is found

**Placeholder Validation**:
- Ensures the adjacent slot is actually a valid placeholder
- Skips if the adjacent position doesn't contain a placeholder
- Handles edge cases (like elements at the very beginning or end)

### When to use NEAR_TO
- When you want ads to appear near specific content types
- For contextual ad placement (e.g., travel ads near travel photos)
- When targeting the first occurrence of important content
- For simple, straightforward proximity-based placement

## NEAR_TO_INDEXES Event Type

### What it does
NEAR_TO_INDEXES is a more advanced version that can place ads near multiple occurrences of a content type at specific positions. Instead of just finding the first image, it can find the 2nd and 4th images and place ads near both.

### How it works
1. The system searches for all occurrences of the specified element type
2. It numbers each occurrence (1st image, 2nd image, 3rd image, etc.)
3. It places ads near only the occurrences at the positions you've specified
4. Each specified position gets its own ad placement

### Configuration Options

#### 1. Element (`element`) - Required
**Purpose**: Specifies what type of content to look for
**How to use**: Same as NEAR_TO, but the system will find ALL occurrences

**Example**: `element: "image"` - Finds all images in the content and numbers them

#### 2. Elements Indexes (`elementsIndexes`) - Required
**Purpose**: Specifies which occurrences of the element to target
**How to use**: Provide an array of position numbers

**Example scenarios**:
- `elementsIndexes: [1, 3]` - Places ads near the 1st and 3rd images
- `elementsIndexes: [2]` - Places an ad near only the 2nd image
- `elementsIndexes: [1, 2, 4]` - Places ads near the 1st, 2nd, and 4th images

#### 3. Position (`position`)
**Purpose**: Same as NEAR_TO - determines above or below placement
**Options**: `"above"` or `"under"`
**Default**: `"above"`

#### 4. Count Backwards (`countBackwards`)
**Purpose**: Changes how elements are numbered
**When `false` (default)**: Counts from the beginning (1st, 2nd, 3rd...)
**When `true`**: Counts from the end (last becomes 1st, second-to-last becomes 2nd...)

**Example with `countBackwards: true`**:
- If there are 5 images, `elementsIndexes: [1]` targets the 5th (last) image
- `elementsIndexes: [2]` targets the 4th image

### Advanced Features

#### Multiple Element Types
Both event types can search for multiple element types:
```
element: ["image", "video", "gallery"]
```
The system will find whichever type appears first (NEAR_TO) or create a combined numbered list (NEAR_TO_INDEXES).

#### Flexible Positioning
The position parameter works consistently across both types:
- `"above"`: Ad appears in the placeholder slot before the target element
- `"under"`: Ad appears in the placeholder slot after the target element

## Key Differences

| Aspect | NEAR_TO | NEAR_TO_INDEXES |
|--------|---------|-----------------|
| **Targeting** | First occurrence only | Multiple specific occurrences |
| **Configuration** | Element + Position | Element + Indexes + Position |
| **Complexity** | Simple, straightforward | More flexible, complex |
| **Use Case** | "Ad near first image" | "Ads near 2nd and 4th images" |
| **Results** | Single ad placement | Multiple ad placements |

## Common Scenarios

### Scenario 1: Ad Near Hero Image
**Use**: NEAR_TO
**Setup**: 
```
element: "hero-image"
position: "under"
```
**Result**: Places one ad below the main hero image

### Scenario 2: Ads Throughout Photo Gallery
**Use**: NEAR_TO_INDEXES
**Setup**: 
```
element: "image"
elementsIndexes: [2, 4, 6]
position: "under"
```
**Result**: Places ads below the 2nd, 4th, and 6th images

### Scenario 3: Ad Near Last Video
**Use**: NEAR_TO_INDEXES with countBackwards
**Setup**: 
```
element: "video"
elementsIndexes: [1]
countBackwards: true
position: "above"
```
**Result**: Places ad above the last video in the content

### Scenario 4: Contextual Content Targeting
**Use**: NEAR_TO with multiple element types
**Setup**: 
```
element: ["infographic", "chart", "data-visualization"]
position: "under"
```
**Result**: Places ad below whichever visual element appears first

## Special Behaviors

### Containing Fact Logic
Both event types have special behavior when no containing fact is explicitly provided:

1. **Automatic Fact Discovery**: The system searches through available content sections to find one containing the target element
2. **Fallback Processing**: Uses a simplified processing method when facts aren't pre-specified
3. **Error Handling**: Returns empty results if no suitable content section is found

### Element Matching
- **Case Sensitive**: Element names must match exactly
- **Type Validation**: Only matches actual content elements, not placeholders
- **First Match Priority**: When multiple element types are specified, uses the first one found

## Error Handling

### NEAR_TO Error Cases
- **Element Not Found**: Returns empty array if target element doesn't exist
- **No Adjacent Placeholder**: Returns empty if there's no placeholder slot near the element
- **Invalid Position**: Handles edge cases gracefully

### NEAR_TO_INDEXES Error Cases
- **Missing Elements Indexes**: Logs warning and returns empty array
- **Index Out of Range**: Safely ignores indexes that don't exist
- **No Matching Elements**: Returns empty if target element type isn't found

## Best Practices

### Content Strategy
1. **Know Your Content**: Understand what element types exist in your content
2. **Test Element Names**: Verify that element names match exactly
3. **Consider Content Variability**: Some content might not have the target elements

### Placement Strategy
1. **User Experience**: Consider how ads near specific content affect readability
2. **Contextual Relevance**: Place ads near content they're related to
3. **Frequency Management**: Don't overwhelm users with too many contextual ads

### Technical Considerations
1. **Performance**: NEAR_TO_INDEXES with many indexes can be more resource-intensive
2. **Fallback Planning**: Have backup placement strategies for content without target elements
3. **Testing**: Test with various content types to ensure consistent behavior

## Troubleshooting

### Common Issues
1. **No Ads Appearing**: Check if target elements exist and are named correctly
2. **Unexpected Placement**: Verify position parameter and element ordering
3. **Missing Some Placements**: Check if all specified indexes have corresponding elements

### Debugging Tips
1. **Element Verification**: Confirm target elements exist in your content
2. **Index Validation**: Ensure elementsIndexes don't exceed available elements
3. **Position Testing**: Try both "above" and "under" to see placement differences
