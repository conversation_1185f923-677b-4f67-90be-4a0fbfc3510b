# Documentation Index

## Overview

This directory contains comprehensive documentation for the ads layouts worker system, with a focus on the placeholder selection functionality.

## Placeholder Selection Documentation

The placeholder selection system determines where advertisements are placed within content. This documentation provides end-user focused explanations of how the system works and how to configure it effectively.

### Main Documentation

- **[Complete Placeholder Selection Guide](placeholder-selection-guide.md)** - Start here for a comprehensive overview of the entire system

### Detailed Event Type Documentation

- **[EVERY_POSITION and EVERY_X Event Types](placeholder-selection-every-position-every-x.md)** - Detailed explanation of position-based and interval-based placement strategies
- **[NEAR_TO and NEAR_TO_INDEXES Event Types](placeholder-selection-near-to-near-to-indexes.md)** - Detailed explanation of content-relative placement strategies

### Practical Resources

- **[Practical Examples](placeholder-selection-examples.md)** - Real-world configuration examples and use cases
- **[Decision Branches Guide](placeholder-selection-decision-branches.md)** - Deep dive into how the system makes placement decisions

## How to Use This Documentation

### For New Users
1. Start with the [Complete Placeholder Selection Guide](placeholder-selection-guide.md)
2. Review the [Practical Examples](placeholder-selection-examples.md) to see configurations in action
3. Refer to specific event type documentation as needed

### For Configuration
1. Identify your placement strategy needs
2. Choose the appropriate event type from the main guide
3. Use the detailed event type documentation for configuration details
4. Test with examples from the practical examples guide

### For Troubleshooting
1. Check the [Decision Branches Guide](placeholder-selection-decision-branches.md) to understand system behavior
2. Review error handling sections in the relevant event type documentation
3. Compare your configuration with working examples

## Documentation Structure

```
docs/
├── README.md                                          # This index file
├── placeholder-selection-guide.md                    # Main comprehensive guide
├── placeholder-selection-every-position-every-x.md   # EVERY_POSITION & EVERY_X details
├── placeholder-selection-near-to-near-to-indexes.md  # NEAR_TO & NEAR_TO_INDEXES details
├── placeholder-selection-examples.md                 # Practical examples collection
└── placeholder-selection-decision-branches.md        # Decision logic deep dive
```

## Key Concepts

- **Placeholder**: A designated slot where an advertisement can be placed
- **Event**: A configuration that defines how and where to place ads
- **Fact**: A section of content (like an article, gallery, or content block)
- **Element**: Individual pieces of content (paragraphs, images, videos, etc.)

## Event Types Summary

| Event Type | Purpose | Best For |
|------------|---------|----------|
| **EVERY_POSITION** | Place ads at specific positions | Consistent placement across similar content |
| **EVERY_X** | Distribute ads at regular intervals | Long content with variable length |
| **NEAR_TO** | Place ads near specific content types | Contextual advertising |
| **NEAR_TO_INDEXES** | Place ads near multiple specific occurrences | Complex contextual strategies |

## Quick Start Examples

### Fixed Position Placement
```json
{
  "type": "everyPosition",
  "params": {
    "placeholder": {
      "placeholderPositions": [1, 3, "last"]
    }
  }
}
```

### Regular Distribution
```json
{
  "type": "everyX",
  "params": {
    "placeholder": {
      "every": 4,
      "max": 3
    }
  }
}
```

### Contextual Placement
```json
{
  "type": "nearTo",
  "params": {
    "placeholder": {
      "element": "image",
      "position": "under"
    }
  }
}
```

## Support and Feedback

This documentation is designed to be comprehensive and user-friendly. If you find areas that need clarification or have suggestions for improvement, please provide feedback to help make the documentation even better.

## Related Resources

- [Ads Layouts dokumentacja użytkownika](https://confluence.pl.grupa.iti/pages/viewpage.action?pageId=175618546) - Main user documentation (Polish)
- Project source code in `src/events/events.service.ts`
- Test files in `test/testFiles/Events/` for technical implementation details
