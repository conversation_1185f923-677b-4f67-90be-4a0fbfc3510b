# EVERY_POSITION and EVERY_X Event Types

## Overview

The EVERY_POSITION and EVERY_X event types are two fundamental ways the system selects where to place advertisements within your content. Think of them as different strategies for choosing the best spots for ads.

## EVERY_POSITION Event Type

### What it does
EVERY_POSITION allows you to specify exact positions where you want placeholders (ad slots) to appear in your content. It's like having a numbered list and saying "I want ads at positions 2, 5, and 8."

### How it works
The system looks at your content and identifies all the available placeholder slots. Then it selects only the ones at the positions you've specified.

### Configuration Options

#### 1. Placeholder Positions (`placeholderPositions`)
**Purpose**: Select placeholders at specific numbered positions
**How to use**: Provide a list of position numbers (starting from 1)

**Example scenarios**:
- `[1, 3, 5]` - Places ads at the 1st, 3rd, and 5th available placeholder slots
- `[2, 4]` - Places ads at the 2nd and 4th placeholder slots
- `["last"]` - Places an ad at the very last placeholder slot

#### 2. Elements Positions (`elementsPositions`)
**Purpose**: Place placeholders relative to specific content elements
**How to use**: Specify which content element and whether the ad should go above or below it

**Format**: `[[element_position, "above"|"under"]]`

**Example scenarios**:
- `[[1, "under"]]` - Places an ad below the 1st content element
- `[[3, "above"]]` - Places an ad above the 3rd content element
- `[[1, "under"], [5, "above"]]` - Places ads below the 1st element and above the 5th element

#### 3. Additional Options

**Placeholder Type Filter (`placeholderType`)**
- Filters to only specific types of placeholders
- Example: Only select "column-placeholder" types

**Count Backwards (`countBackwards`)**
- When `true`, counts positions from the end instead of the beginning
- Example: Position 1 becomes the last position, position 2 becomes second-to-last

**Enable Variant (`enableVariant`)**
- Considers content variants when calculating positions
- Useful when different content versions affect positioning

**Device Type Consideration**
- The system considers whether users are on desktop, mobile, or tablet
- This can affect which placeholders are selected

### When to use EVERY_POSITION
- When you know exactly where you want ads to appear
- For consistent ad placement across similar content
- When you need precise control over ad positioning

## EVERY_X Event Type

### What it does
EVERY_X places advertisements at regular intervals throughout your content. Instead of specifying exact positions, you say "place an ad every 3 content pieces" or "every 5 paragraphs."

### How it works
The system counts through your content elements and places an ad placeholder every X elements, where X is the number you specify.

### Configuration Options

#### 1. Every (`every`) - Required
**Purpose**: Defines the interval for ad placement
**How to use**: Specify a number representing how often to place ads

**Example scenarios**:
- `every: 3` - Places an ad after every 3rd content element
- `every: 5` - Places an ad after every 5th content element
- `every: 2` - Places an ad after every 2nd content element

#### 2. Position (`position`)
**Purpose**: Determines whether ads go above or below the target element
**Options**: "above" or "under"
**Default**: "above"

#### 3. Element Filter (`element`)
**Purpose**: Only count specific types of content when calculating intervals
**How to use**: Specify which content types to include in the counting

**Example**: If you only want to count "article" elements, ads will be placed every X articles, ignoring other content types.

#### 4. Advanced Options

**Maximum Count (`max`)**
- Limits the total number of ads placed
- Example: `max: 3` means no more than 3 ads will be placed, even if the interval would allow more

**Omit Last (`ommitLast`)**
- When `true`, removes the last placeholder if it exists
- Useful for avoiding ads at the very end of content

**Start Index (`startIndex`)**
- Begins counting from a specific position instead of the beginning
- Example: `startIndex: 2` starts counting from the 2nd element

**Exclude Logic (`exclude`)**
- Provides more sophisticated placement logic
- Works with position preferences to avoid certain placements

### When to use EVERY_X
- For consistent ad distribution throughout long content
- When content length varies significantly
- For maintaining regular ad frequency without manual positioning

## Key Differences

| Aspect | EVERY_POSITION | EVERY_X |
|--------|----------------|---------|
| **Control Level** | Precise, exact positions | Flexible, interval-based |
| **Best For** | Known content structure | Variable content length |
| **Predictability** | Highly predictable | Adapts to content |
| **Configuration** | Position numbers or element relationships | Interval numbers |
| **Use Case** | "Ad at position 3 and 7" | "Ad every 4 paragraphs" |

## Common Scenarios

### Scenario 1: News Article with Fixed Ad Positions
**Use**: EVERY_POSITION with `elementsPositions`
**Setup**: `[[2, "under"], [6, "under"]]`
**Result**: Ads appear after the 2nd and 6th paragraphs

### Scenario 2: Long Blog Post with Regular Ad Distribution
**Use**: EVERY_X
**Setup**: `every: 4, position: "under"`
**Result**: Ad appears after every 4th content element

### Scenario 3: Mobile-Optimized Ad Placement
**Use**: EVERY_POSITION with device type consideration
**Setup**: Different positions for mobile vs desktop users
**Result**: Optimized ad placement based on screen size

## Error Handling

Both event types include built-in error handling:

- **Invalid Configuration**: Returns empty results with appropriate logging
- **Missing Required Parameters**: Logs warnings and returns no placeholders
- **Out of Range Positions**: Safely ignores invalid positions
- **Empty Content**: Gracefully handles content with no available placeholders

## Best Practices

1. **Test Different Configurations**: Try various settings to find optimal ad placement
2. **Consider User Experience**: Avoid overwhelming users with too many ads
3. **Device Optimization**: Use different strategies for different device types
4. **Content Type Awareness**: Consider how different content types affect ad effectiveness
5. **Performance Monitoring**: Track how different placements perform
