import { Modu<PERSON> } from '@nestjs/common';
import { InfoModule } from './info/info.module';
import { MongooseModule } from '@nestjs/mongoose';
import { GeneratorModule } from './generator/generator.module';
import { AppCacheModule } from './appCache/appCache.module';
import { ScheduleModule } from '@nestjs/schedule';
import { ENV } from './envalidConfig';
import { CacheModule } from './cacheModule/cache.module';
import { BlacklistModule } from './blacklist/blacklist.module';

@Module({
  imports: [
    MongooseModule.forRoot(ENV.MONGO_HOST, {
      dbName: ENV.MONGO_DB_NAME,
      pass: ENV.MONGO_PASS,
      user: ENV.MONGO_USER,
      autoCreate: false,
      autoIndex: false
    }),
    CacheModule.register({ isActive: ENV.APP_CACHE === 'ENABLED' }),
    InfoModule,
    BlacklistModule,
    AppCacheModule,
    GeneratorModule,
    ScheduleModule.forRoot()
  ]
})
export class SlaveModule {}

@Module({
  imports: [InfoModule, BlacklistModule]
})
export class MasterModule {}
