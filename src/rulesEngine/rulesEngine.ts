import { findObjectInNestedObject, getAllFactsFromRequestObject } from 'Helpers';
import {
  BodyElement,
  CommonRequest,
  IEngineSetupResponse,
  RulesQueryArgs
} from 'InterfacesAndTypes';
import log from 'Logger/logger';
import { LogLevel, OperatorEnum } from 'ads-layouts-tools';
import * as JRE from 'json-rules-engine';
import { containsOfTypeOperator, expectedPositionOperator } from '../generator/operators';
import { RuleService } from '../rules/rules.service';
import { ServiceToPackageMapService } from '../serviceToPackageMap/serviceToPackageMap.service';

export class RulesEngine {
  protected engine!: JRE.Engine;
  protected ruleService: RuleService;
  protected serviceToPackageMapService: ServiceToPackageMapService;

  constructor(
    ruleService: RuleService,
    serviceToPackageMapService: ServiceToPackageMapService
  ) {
    this.ruleService = ruleService;
    this.serviceToPackageMapService = serviceToPackageMapService;
  }

  protected engineSetup = async (
    content: CommonRequest,
    omitCache = false
  ): Promise<IEngineSetupResponse> => {
    this.engine = new JRE.Engine(undefined, { allowUndefinedFacts: true });

    const rulesPackage = await this.serviceToPackageMapService.getRulesPackageForService(
      content.meta,
      omitCache
    );

    const allFactsNames = await this.addEngineFacts(content);
    log('ALL_FACTS_NAMES', { allFactsNames }, LogLevel.dev);

    await this.addEngineOperators();
    await this.addEngineRules(
      { rulesPackage, type: content.type, contentMeta: content.meta },
      omitCache
    );

    return {
      allFactsNames,
      rulesPackage
    };
  };

  protected async addEngineFacts(content: CommonRequest): Promise<string[]> {
    const allFacts = getAllFactsFromRequestObject(content);

    allFacts.forEach(factName => {
      this.engine.addFact(factName, async (_, almanac) => {
        const elements: BodyElement[] | undefined = await almanac.factValue('elements');
        if (!elements) {
          return;
        }
        return findObjectInNestedObject(elements, 'type', factName, 'elements');
      });
    });

    return allFacts;
  }

  protected async addEngineOperators(): Promise<void> {
    await this.engine.addOperator(OperatorEnum.CONTAINS_OF_TYPE, containsOfTypeOperator);
    await this.engine.addOperator(OperatorEnum.EXPECTED_POSITION, expectedPositionOperator);
  }

  protected async addEngineRules(args: RulesQueryArgs, omitCache = false): Promise<void> {
    const { rulesPackage } = args;
    const pageRules = await this.ruleService.getPageRules(args, omitCache);

    const addedRules: object[] = [];
    const incompleteRules: object[] = [];

    for (const rule of JSON.parse(JSON.stringify(pageRules))) {
      const { name, conditions, event, priority } = rule;
      if (!name || !conditions || !event) {
        incompleteRules.push({ name, conditions, event, priority });
        continue;
      }

      addedRules.push({
        rulesPackage,
        name,
        conditions,
        event,
        priority
      });

      this.engine.addRule({
        name,
        conditions,
        event,
        priority
      });
    }

    log('ADDED_RULES', { addedRules }, LogLevel.dev);

    if (incompleteRules.length) {
      log('INCOMPLETE_RULES', { incompleteRules }, LogLevel.warn);
    }
  }
}
