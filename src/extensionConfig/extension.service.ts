import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  ExtEnableDataEnum,
  ExtensionConfig,
  ExtensionConfigDocument,
  ExtScheduleDataEnum
} from 'ads-layouts-tools';
import * as dayjs from 'dayjs';
import { deHydrateDocument } from 'Helpers';
import { ExtensionKey } from 'InterfacesAndTypes';
import { Model } from 'mongoose';
import { CacheService } from '../cacheModule/cache.service';
import { ENV } from '../envalidConfig';

@Injectable()
export class ExtensionService {
  constructor(
    @InjectModel(ExtensionConfig.name)
    private extensionConfigModel: Model<ExtensionConfigDocument>,
    private readonly cache: CacheService
  ) {}

  async getExtensionConfig(
    serviceId: string,
    omitCache = false
  ): Promise<ExtensionConfig | null> {
    const key: ExtensionKey = `${CachePartsEnum.EXTENSION}__${serviceId}`;
    let extensionDocument = await this.cache.get(key, omitCache);

    if (!extensionDocument) {
      extensionDocument = await this.extensionConfigModel.findOne({});

      if (!extensionDocument) {
        return null;
      }

      await this.cache.set(key, extensionDocument, omitCache, ENV.LOCAL_CACHE_EXTENSION_TTL);
    }

    return deHydrateDocument(extensionDocument);
  }

  async checkExtensionStatus(
    serviceId: string,
    extType: ExtEnableDataEnum,
    omitCache = false
  ): Promise<boolean> {
    const extension = await this.getExtensionConfig(serviceId, omitCache);

    return extension?.[extType]?.[serviceId] ?? true;
  }

  async checkExtensionScheduleStatus(
    serviceId: string,
    extType: ExtScheduleDataEnum,
    metaTime: number,
    omitCache = false
  ): Promise<boolean> {
    const extension = await this.getExtensionConfig(serviceId, omitCache);

    const serviceScheduleForService = extension?.[extType]?.[serviceId];

    if (serviceScheduleForService?.enabled) {
      const { start, end } = serviceScheduleForService;

      return dayjs(metaTime).isAfter(start) && dayjs(metaTime).isBefore(end);
    }

    return true;
  }
}
