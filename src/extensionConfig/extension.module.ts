import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ExtensionService } from './extension.service';
import {
  ExtensionConfig,
  ExtensionConfigSchema,
  AdConfig,
  AdConfigSchema
} from 'ads-layouts-tools';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ExtensionConfig.name,
        schema: ExtensionConfigSchema
      },
      {
        name: AdConfig.name,
        schema: AdConfigSchema
      }
    ])
  ],
  providers: [ExtensionService],
  exports: [ExtensionService]
})
export class ExtensionModule {}
