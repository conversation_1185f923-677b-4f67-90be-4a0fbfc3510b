import { HttpException, HttpExceptionOptions, HttpStatus } from '@nestjs/common';
import * as dayjs from 'dayjs';
import logger from 'Logger/logger';
import { CustomHttpStatus, ErrorInterface, LogLevel } from 'ads-layouts-tools';
import { tracerUtils } from 'Logger/trace';
import { error2XXhelper } from 'Helpers';

class CustomException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus | CustomHttpStatus,
    options?: HttpExceptionOptions
  ) {
    super(
      {
        statusCode,
        message,
        dateTime: dayjs().format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        traceId: tracerUtils.getActiveTraceId()
      },
      statusCode,
      options
    );
  }
}

class UnknownException extends HttpException {
  constructor(message: string, options?: HttpExceptionOptions) {
    super(
      {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: `Unknown error: ${message}`,
        dateTime: dayjs().format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        traceId: tracerUtils.getActiveTraceId()
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
      options
    );
  }
}

export function CreateException(
  error: CustomException | ErrorInterface | Error,
  options?: HttpExceptionOptions,
  extraLoggerInfo = ''
): HttpException {
  if (error instanceof CustomException) {
    return error;
  }

  if (isErrorInterface(error)) {
    const { message, statusCode } = error;

    if (error2XXhelper(statusCode)) {
      logger(`ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
    }

    if (!message) {
      logger(`CRITICAL_ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
      return new UnknownException('Error message is missing', options);
    }

    return new CustomException(message, statusCode, options);
  }

  if (error instanceof Error) {
    logger(`ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
    return new UnknownException(error.message, options);
  }

  logger(`CRITICAL_ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
  return new UnknownException('This error should never happen', options);
}

export function UpdateException(error: any, updateData: Record<string, any>) {
  error.response = { ...error.response, ...updateData };

  return error;
}

const isErrorInterface = (error: unknown): error is ErrorInterface =>
  typeof error === 'object' && error !== null && 'statusCode' in error && 'message' in error;
