import {
  Catch,
  ExceptionFilter,
  HttpException,
  BadRequestException,
  NotFoundException,
  HttpStatus
} from '@nestjs/common';
import { MongoError } from 'mongodb';
import { Error as ValidatorError } from 'mongoose';
import { ErrorInterface } from 'ads-layouts-tools';
import { CreateException } from './exceptions';

@Catch(MongoError)
export class MongoExceptionFilter implements ExceptionFilter {
  catch(exception: MongoError): void {
    const { message, response: { statusCode, reqId } = {} as Partial<ErrorInterface> } =
      exception as any;

    throw CreateException({ message, statusCode, reqId });
  }
}

@Catch(BadRequestException, NotFoundException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException): void {
    let { message, statusCode, reqId } = exception.getResponse() as Partial<ErrorInterface>;

    if (message === undefined) {
      message = exception.message;
    }

    if (statusCode === undefined) {
      statusCode = HttpStatus.BAD_REQUEST;
    }

    throw CreateException({ message, statusCode, reqId });
  }
}

@Catch(ValidatorError)
export class MongooseValidatorExceptionFilter implements ExceptionFilter {
  catch(exception: ValidatorError): void {
    const { message, response: { statusCode = 422 } = {} } = exception as any;

    throw CreateException({ message, statusCode });
  }
}
