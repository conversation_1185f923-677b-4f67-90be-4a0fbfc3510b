import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { GeneratorModule } from './generator/generator.module';
import { InfoModule } from './info/info.module';

export function initSwagger(app: NestFastifyApplication): void {
  const config = new DocumentBuilder().setTitle('Ads Layouts').build();

  const swaggerCustomOptions = {
    customCss: '.swagger-ui section.models { visibility: hidden;}'
  };

  const document = SwaggerModule.createDocument(app, config, {
    include: [GeneratorModule, InfoModule]
  });

  SwaggerModule.setup('doc', app, document, swaggerCustomOptions);
}
