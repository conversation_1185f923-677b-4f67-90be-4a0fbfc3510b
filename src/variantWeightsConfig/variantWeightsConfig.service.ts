import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  LogLevel,
  VariantWeightsConfig,
  VariantWeightsConfigDocument,
  WeightsConfig
} from 'ads-layouts-tools';
import { deHydrateDocument } from 'Helpers';
import { VariantKey } from 'InterfacesAndTypes';
import log from 'Logger/logger';
import { Model } from 'mongoose';
import { CacheService } from '../cacheModule/cache.service';
import { ENV } from '../envalidConfig';

@Injectable()
export class VariantWeightsConfigService {
  private readonly key: VariantKey = `${CachePartsEnum.VARIANT}__`;

  constructor(
    @InjectModel(VariantWeightsConfig.name)
    private variantWeightsConfigModel: Model<VariantWeightsConfigDocument>,
    private readonly cache: CacheService
  ) {}

  async get(omitCache = false): Promise<WeightsConfig | null> {
    let variantWeightsConfigsDocument = await this.cache.get(this.key, omitCache);

    if (!variantWeightsConfigsDocument) {
      variantWeightsConfigsDocument = await this.variantWeightsConfigModel
        .findOne({})
        .select('-createdAt -updatedAt -_id -__v')
        .exec();

      if (!variantWeightsConfigsDocument) {
        log('VARIANT_WEIGHTS_CONFIG_NOT_FOUND', {}, LogLevel.dev);
        return null;
      }

      await this.cache.set(
        this.key,
        variantWeightsConfigsDocument,
        omitCache,
        ENV.LOCAL_CACHE_VARIANT_WEIGHTS_TTL
      );
    }

    const variantWeightsConfigs = deHydrateDocument(variantWeightsConfigsDocument);

    await this.cache.reCalculateCacheHash();

    return variantWeightsConfigs.weights;
  }
}
