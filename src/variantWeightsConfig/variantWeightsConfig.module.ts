import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { VariantWeightsConfigService } from './variantWeightsConfig.service';
import { VariantWeightsConfig, VariantWeightsConfigSchema } from 'ads-layouts-tools';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: VariantWeightsConfig.name,
        schema: VariantWeightsConfigSchema
      }
    ])
  ],
  providers: [VariantWeightsConfigService],
  exports: [VariantWeightsConfigService]
})
export class VariantWeightsConfigModule {}
