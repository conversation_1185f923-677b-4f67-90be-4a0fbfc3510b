/**
 * This type alias, KeysWithArrayValues, is a mapped type that extracts the keys of a type T that have string array values. It returns a union type of these keys.

In other words, it helps to narrow down the keys of an object to only those that have string array values.
 *
 * @example
 * type MyType = {
 *   foo: string[];
 *   bar: string
 *   baz: number[]
 * }
 *
 * type MyTypeArrayKeys = KeysWithArrayValues<MyType>
 * // type MyTypeArrayKeys = 'foo' | 'baz'
 */
export type KeysWithArrayValues<T> = string &
  {
    [K in keyof T]: NonNullable<T[K]> extends Array<any> ? K : never;
  }[keyof T];
