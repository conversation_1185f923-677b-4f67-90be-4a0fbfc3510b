import { PageTypeEnum } from 'InterfacesAndTypes';
import {
  AccessModelEnum,
  AdConfigDeviceTypeEnum,
  PaywallEnum,
  Prettify
} from 'ads-layouts-tools';

export type ContentMetaRequiredFields = {
  deviceType: AdConfigDeviceTypeEnum;
  locationInfoPageId: string | null;
  locationInfoPageType: string;
  locationInfoSectionId: string;
  locationInfoSectionName: string;
  serviceId: string;
  siteVersionIdentifier: string;
  time: string;
};

export type ContentMetaOptionalFields = Partial<{
  accessModel: AccessModelEnum | string;
  paywall: PaywallEnum | string;
  rulesPackage: string;
  serviceEnv: string;
  siteVersion: string;
}>;

export type ContentMeta = Prettify<ContentMetaRequiredFields & ContentMetaOptionalFields>;

export type MetaElement =
  | { length?: number }
  | { height?: number }
  | { count?: number }
  | { element?: string }
  | { columns?: number; position?: number; title?: string }
  | { column?: number }
  | { variant?: string };

export interface BodyElement {
  type: string;
  id?: string;
  meta?: MetaElement;
  elements?: BodyElement[];
  direction?: string;
}

export interface CommonRequest {
  type: PageTypeEnum;
  meta: ContentMeta;
  elements: BodyElement[];
  direction: string;
}
