import { IMatchedPlaceholdersWithAdConfigs, PlaceholderType } from 'InterfacesAndTypes';

export type RuleStats = {
  ruleName: string;
  conditionPass: boolean;
  eventPass?: boolean;
  mergePass?: boolean;
  mergeResult?:
    | {
        id: string;
        type: string;
        AD_Config_group: string;
        AD_Config_element_id: string;
      }[]
    | {
        placeholders: any;
        eventAdConfigGroup: string;
      };
  selectedPlaceholders?: PlaceholderType[];
};

export type MergePlaceholdersWithAdConfigsBaseOnEventsResult = {
  successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[];
  failedPlaceholdersMerge: PlaceholderType[];
  rulesStats: RuleStats[];
};
