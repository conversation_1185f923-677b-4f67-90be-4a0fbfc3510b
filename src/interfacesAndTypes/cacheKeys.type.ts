import {
  AdConfigDeviceTypeEnum,
  AdConfigDocument,
  CachePartsEnum,
  DisplayConfigDocument,
  ExtensionConfigDocument,
  RuleDocument,
  ServiceToPackageMapDocument,
  VariantWeightsConfigDocument
} from 'ads-layouts-tools';

type ServiceId = string;
type Time = string;
type PageType = string;
type Release = string;
type DeviceType = AdConfigDeviceTypeEnum;
type SHA1 = string;
type AnyString = string;

export type AdConfigKey =
  `${CachePartsEnum.AD_CONFIGS}__${ServiceId}__${PageType}__${Release}`;

export type DisplayConfigKey = `${CachePartsEnum.RELEASE}__${ServiceId}__${Time}`;

export type RulesPackageKey = `${CachePartsEnum.RULES_PACKAGE}__${ServiceId}`;

// Base key plus optional rulesPackage, siteVersion, accessModel, paywall
export type RulesKey =
  | `${CachePartsEnum.RULES}__${ServiceId}__${PageType}__${DeviceType}`
  | `${CachePartsEnum.RULES}__${ServiceId}__${PageType}__${DeviceType}__${AnyString}`;

export type ExtensionKey = `${CachePartsEnum.EXTENSION}__${ServiceId}`;

export type VariantKey = `${CachePartsEnum.VARIANT}__${AnyString}`;

export type KeyToDataType = {
  [key: DisplayConfigKey]: DisplayConfigDocument[];
  [key: AdConfigKey]: AdConfigDocument[];
  [key: RulesKey]: RuleDocument[];
  [key: RulesPackageKey]: ServiceToPackageMapDocument;
  [key: ExtensionKey]: ExtensionConfigDocument;
  [key: VariantKey]: VariantWeightsConfigDocument;
};

export type AppCacheKeyType = keyof KeyToDataType;
export type CacheID = `${AppCacheKeyType}__${SHA1}`;

export type cachePrefix = `${CachePartsEnum}__${ServiceId}`;
