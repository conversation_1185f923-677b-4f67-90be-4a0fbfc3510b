import { AdConfigActivationThresholds, PlaceholdersDetails } from 'ads-layouts-tools';

export interface ICommonConfigFields {
  masterId: string;
  bgPlugSrc: string | null;
  activationThresholds: AdConfigActivationThresholds;
  trafficCategory: string[];
}

export interface IFilteredAdConfigs {
  placeholders: PlaceholdersDetails[];
  commonConfigFields: ICommonConfigFields;
  configName: string;
  serviceIdFromConfig: string[];
  modifiedDate: string;
}
