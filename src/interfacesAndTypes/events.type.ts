import { PlaceholderPositionEnum, Prettify } from 'ads-layouts-tools';

type NonPlaceholderIndex = { nonPlaceholderIndex: number };

export type FactBase = { type: string };

export type FactType = Prettify<
  FactBase & {
    id: string;
    meta?: { height: number; variant?: string };
  } & Partial<NonPlaceholderIndex>
>;

export type FactTypeWithElements = Prettify<FactType & { elements: FactTypeWithElements[] }>;

export type PlaceholderType = Pick<FactType, 'id' | 'type'>;

export enum PlaceholderEnum {
  PLACEHOLDER = 'placeholder',
  COLUMN_PLACEHOLDER = 'column-placeholder'
}

export type PosFactType = Prettify<FactType & NonPlaceholderIndex>;

type CanBeUsed = { canBeUsed: boolean };

export type PlaceholderTypeUsable = Prettify<PlaceholderType & CanBeUsed>;

type NeighboringPlaceholders = Record<PlaceholderPositionEnum, PlaceholderTypeUsable | null>;

export type FactWithNeighbors = Prettify<PosFactType & CanBeUsed & NeighboringPlaceholders>;
