import { IEvent } from 'ads-layouts-tools';
import {
  Almanac as <PERSON><PERSON><PERSON><PERSON>,
  EngineResult as JREEngineResult,
  RuleResult as JRERuleResult,
  TopLevelConditionResult as JRETopLevelConditionResult
} from 'json-rules-engine';

export interface IAlmanacEvents {
  success: IEvent[];
  failure: IEvent[];
}

export interface IAlmanac extends JREAlmanac {
  factMap: FactMap;
  factResultsCache: FactMap;
  events: IAlmanacEvents;
  ruleResults: IRuleResult[];
  allowUndefinedFacts: boolean;
}

export interface IRuleResult extends JRERuleResult {
  event: IEvent;
  result: boolean;
  conditions: JRETopLevelConditionResult & {
    rulesPackage: string;
  };
}

export interface IEngineResult extends JREEngineResult {
  almanac: IAlmanac;
  results: IRuleResult[];
  failureResults: IRuleResult[];
  events: IEvent[];
  failureEvents: IEvent[];
}

type FactMap = Record<string, any>;
