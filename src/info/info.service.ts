import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const cluster = require('cluster');
import { ENV } from '../envalidConfig';
import { IAppInfo } from 'InterfacesAndTypes';
import * as os from 'os';
import { ServiceEnvEnum } from 'ads-layouts-tools';

interface IWorkerState {
  cluster: number;
  state: string;
}

const workerStartedTime = new Date();
@Injectable()
export class InfoService {
  get appInfo(): IAppInfo {
    return {
      maxCpu: this.cpuQuantity,
      version: this.appVersion,
      nodeVersion: this.nodeVersion,
      environment: this.environment,
      currentMachineTime: this.currentMachineTime,
      workerStartedTime
    };
  }

  get cpuQuantity(): number {
    return os.cpus().length - ENV.SLAVE_COUNT_REDUCE;
  }

  get nodeVersion(): string {
    return process.version;
  }

  get appVersion(): string {
    return process.env.npm_package_version ?? 'npm package version not found';
  }

  get environment(): ServiceEnvEnum {
    return ENV.APP_ENV;
  }

  get currentMachineTime(): Date {
    return new Date();
  }

  get workerStartedTime(): Date {
    return workerStartedTime;
  }

  healthCheck(): string {
    if (cluster.isPrimary) {
      const { liveWorkers, shouldBeAlive } = this.countLiveWorkers();

      const minimumListeningWorkerAmount = Math.floor(
        (shouldBeAlive.length * ENV.HEALTH_CHECK_LIMIT) / 100
      );

      if (liveWorkers > minimumListeningWorkerAmount) {
        return JSON.stringify(shouldBeAlive);
      }
      throw new HttpException('HEALTH_CHECK_ERROR', HttpStatus.BAD_REQUEST);
    } else {
      return 'OK';
    }
  }

  private countLiveWorkers() {
    const shouldBeAlive: IWorkerState[] = [];
    for (const i in cluster.workers) {
      if (cluster.workers[i].process.pid !== process.pid) {
        const { id, state } = cluster.workers[i];
        const current = { cluster: id, state };
        shouldBeAlive.push(current);
      }
    }
    const liveWorkers = shouldBeAlive.filter(y => y.state === 'listening').length;
    return { liveWorkers, shouldBeAlive };
  }
}
