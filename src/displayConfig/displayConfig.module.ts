import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DisplayConfigService } from './displayConfig.service';
import { DisplayConfig, DisplayConfigSchema } from 'ads-layouts-tools';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: DisplayConfig.name,
        schema: DisplayConfigSchema
      }
    ])
  ],
  providers: [DisplayConfigService],
  exports: [DisplayConfigService]
})
export class DisplayConfigModule {}
