import { Injectable, OnModuleInit } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { getPlaceholdersFromFact, isPlaceholder, returnAsArrayEmpty } from 'Helpers';
import {
  FactType,
  FactTypeWithElements,
  FactWithNeighbors,
  IAlmanac,
  PlaceholderType,
  PlaceholderTypeUsable,
  PosFactType
} from 'InterfacesAndTypes';
import log from 'Logger/logger';
import {
  AdConfigDeviceTypeEnum,
  EventTypeEnum,
  IEvent,
  LogLevel,
  PlaceholderPositionEnum,
  WeightsConfig
} from 'ads-layouts-tools';
import { cloneDeep } from 'lodash';
import { ENV } from '../envalidConfig';
import { VariantWeightsConfigService } from '../variantWeightsConfig/variantWeightsConfig.service';

const above = PlaceholderPositionEnum.ABOVE;
const under = PlaceholderPositionEnum.UNDER;

@Injectable()
export class EventsService implements OnModuleInit {
  private variantWeights: WeightsConfig | null = null;

  constructor(private readonly variantService: VariantWeightsConfigService) {}

  async onModuleInit() {
    log('EVENTS_SERVICE_INIT', {}, LogLevel.dev);
    await this.updateVariantWeights();
  }

  @Interval(ENV.LOCAL_CACHE_VARIANT_WEIGHTS_TTL)
  async updateVariantWeights(omitCache = false) {
    this.variantWeights = await this.variantService.get(omitCache);
  }

  async getSiteMapPlaceholdersBasedOnEvents(
    successfulEvent: IEvent,
    resultAlmanac: IAlmanac,
    allFactsNames: string[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<PlaceholderType[]> {
    const hasContainingFact = !!successfulEvent?.params?.fact?.length;

    let siteMapMatchedPlaceholders: PlaceholderType[] = [];

    if (hasContainingFact) {
      // containing fact for event set explicitly
      siteMapMatchedPlaceholders = await this.getPlaceholdersByEventType(
        successfulEvent,
        resultAlmanac,
        allFactsNames,
        deviceType
      );
    } else {
      // containing fact for event not provided
      const almanacFactsNames: string[] = resultAlmanac.factMap.keys();

      const placeholdersElements = returnAsArrayEmpty(
        successfulEvent.params.placeholder.element
      );

      const containingFact = await this.getContainingFact(
        almanacFactsNames,
        placeholdersElements,
        resultAlmanac
      );

      if (!containingFact) {
        return [];
      }

      siteMapMatchedPlaceholders = this.getPlaceholdersByEventTypeForNotProvidedContainingFact(
        successfulEvent,
        containingFact
      );
    }

    return siteMapMatchedPlaceholders;
  }
  private async getContainingFact(
    almanacFactsNames: string[],
    placeholdersElements: string[],
    resultAlmanac: IAlmanac
  ): Promise<FactType[] | undefined> {
    for (const fact of almanacFactsNames) {
      const factContent: FactType[] = await resultAlmanac.factValue(fact);

      if (factContent) {
        const factContentPossibleArray = returnAsArrayEmpty(factContent);

        for (const placeholderElement of placeholdersElements) {
          const checkFactContent = factContentPossibleArray.find(
            f => f.type === placeholderElement
          );
          if (checkFactContent) {
            return factContent;
          }
        }
      }
    }
  }

  async getPlaceholdersByEventType(
    event: IEvent,
    almanac: IAlmanac,
    allFactsNames: string[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<PlaceholderType[]> {
    const {
      fact,
      factPosition,
      parentFact,
      searchInFacts,
      excludeFromSearch,
      containsSomeType
    } = event.params;
    // multiple fact container

    const AnyFactAccepted =
      event?.type === EventTypeEnum.NEAR_TO_INDEXES && fact.includes('any');

    const factNames = AnyFactAccepted ? allFactsNames : fact;

    if (containsSomeType) {
      const multipleFactsPlaceholders: PlaceholderType[][] = [];

      for (const eventFact of returnAsArrayEmpty(factNames)) {
        const factValue: FactTypeWithElements[] | undefined =
          await almanac.factValue(eventFact);

        if (!factValue || !factValue.length) {
          continue;
        }

        let factsToSearch =
          searchInFacts[0] === 'all'
            ? factValue
            : factValue.filter(el => searchInFacts.includes(el.type));

        if (excludeFromSearch?.length) {
          factsToSearch = factsToSearch.filter(el => !excludeFromSearch.includes(el.type));
        }

        const expectedFacts = factsToSearch.filter(el => {
          return el.elements?.some(item => item.type === containsSomeType);
        });

        for (const expectedFact of expectedFacts) {
          const { elements } = expectedFact;

          switch (event.type) {
            case EventTypeEnum.EVERY_POSITION:
              multipleFactsPlaceholders.push(
                this.getEveryPositionPlaceholder(event, elements, deviceType)
              );
              break;

            case EventTypeEnum.EVERY_X:
              multipleFactsPlaceholders.push(this.getEveryXPlaceholder(event, elements));
              break;

            case EventTypeEnum.NEAR_TO:
              multipleFactsPlaceholders.push(
                this.getNearToElementPlaceholder(event, elements)
              );
              break;

            case EventTypeEnum.NEAR_TO_INDEXES:
              multipleFactsPlaceholders.push(this.getPlaceholdersNearIndexes(event, elements));
              break;
          }
        }

        return multipleFactsPlaceholders.flat();
      }
    }

    // single fact container
    for (const eventFact of factNames) {
      if (allFactsNames.includes(eventFact)) {
        let expectedFactElements: FactType[] | null | undefined = null;

        // ensure the fact is in the right position
        if (factPosition && parentFact) {
          let expectedFactElementsArr: FactTypeWithElements[] | undefined =
            await almanac.factValue(parentFact);

          if (!expectedFactElementsArr || !expectedFactElementsArr.length) {
            continue;
          }

          if (!AnyFactAccepted) {
            expectedFactElementsArr = expectedFactElementsArr.filter(
              el => el.type === eventFact
            );
          } else {
            expectedFactElementsArr = expectedFactElementsArr.filter(
              el => !isPlaceholder(el.type)
            );
          }

          expectedFactElementsArr = expectedFactElementsArr.filter(
            (_, index) => ++index === factPosition
          );

          if (expectedFactElementsArr.length > 0) {
            expectedFactElements = expectedFactElementsArr[0].elements;
          }
        } else {
          expectedFactElements = await almanac.factValue(eventFact);
        }

        if (!expectedFactElements) {
          continue;
        }

        switch (event.type) {
          case EventTypeEnum.EVERY_POSITION:
            return this.getEveryPositionPlaceholder(event, expectedFactElements, deviceType);

          case EventTypeEnum.EVERY_X:
            return this.getEveryXPlaceholder(event, expectedFactElements);

          case EventTypeEnum.NEAR_TO:
            return this.getNearToElementPlaceholder(event, expectedFactElements);

          case EventTypeEnum.NEAR_TO_INDEXES:
            return this.getPlaceholdersNearIndexes(event, expectedFactElements);
        }
      }
    }

    // should never happen
    log('EVENT_TYPE_DID_NOT_MATCH', { event }, LogLevel.dev);
    return [];
  }

  getPlaceholdersByEventTypeForNotProvidedContainingFact(
    event: IEvent,
    containingFact: FactType[]
  ): PlaceholderType[] {
    switch (event.type) {
      case EventTypeEnum.NEAR_TO:
        return this.getNearToElementPlaceholder(event, containingFact);

      case EventTypeEnum.NEAR_TO_INDEXES:
        return this.getPlaceholdersNearIndexes(event, containingFact);

      default:
        log('INVALID_FUNCTION_INVOCATION_FOR_GIVEN_EVENT_TYPE', { event }, LogLevel.warn);
        return [];
    }
  }

  getEveryPositionPlaceholder(
    event: IEvent,
    fact: FactType[],
    deviceType: AdConfigDeviceTypeEnum
  ): PlaceholderType[] {
    const {
      placeholderPositions,
      placeholderType,
      elementsPositions,
      countBackwards,
      enableVariant
    } = event.params.placeholder;

    if (placeholderPositions?.length) {
      let placeholdersElementsOnly = getPlaceholdersFromFact(fact);

      if (placeholderType) {
        placeholdersElementsOnly = placeholdersElementsOnly.filter(
          el => el.type === placeholderType
        );
      }

      if (countBackwards) {
        placeholdersElementsOnly = placeholdersElementsOnly.reverse();
      }

      const filteredPlaceholders: PlaceholderType[] = [];

      if (placeholderPositions.some(el => el === 'last') && !countBackwards) {
        filteredPlaceholders.push(
          placeholdersElementsOnly[placeholdersElementsOnly.length - 1]
        );
      }

      filteredPlaceholders.push(
        ...placeholdersElementsOnly.filter((_, index) =>
          placeholderPositions.includes(index + 1)
        )
      );

      return filteredPlaceholders;
    }

    if (elementsPositions?.length) {
      const selectedElements: FactType[] = [];

      let nonPlaceholderIndex = 0;

      const factsInRightOrder = countBackwards ? cloneDeep(fact).reverse() : fact;

      const factWithNonPlaceholderPositionField = factsInRightOrder.map(el => {
        if (isPlaceholder(el.type)) {
          return {
            ...el,
            nonPlaceholderIndex: undefined
          };
        }

        nonPlaceholderIndex += enableVariant
          ? this.getVariantWeight(deviceType, el.type, el.meta?.variant)
          : 1;

        return {
          ...el,
          nonPlaceholderIndex
        };
      });

      for (const [position, location] of elementsPositions) {
        const nonPlaceholderElementIndexInFact = factWithNonPlaceholderPositionField.findIndex(
          el => el.nonPlaceholderIndex === position
        );

        if (nonPlaceholderElementIndexInFact !== -1) {
          const getPlaceholderIndex =
            location === PlaceholderPositionEnum.UNDER
              ? nonPlaceholderElementIndexInFact + 1
              : nonPlaceholderElementIndexInFact - 1;

          const placeholder = factWithNonPlaceholderPositionField[getPlaceholderIndex];

          if (isPlaceholder(placeholder.type)) {
            selectedElements.push(placeholder);
          }
        }
      }

      return selectedElements.map((el): PlaceholderType => {
        const { nonPlaceholderIndex, meta, ...other } = el;
        return other;
      });
    }

    log('INVALID_EVENT', { event }, LogLevel.dev);
    return [];
  }

  getEveryXPlaceholder(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { every, max, ommitLast, position, element, startIndex, exclude } =
      event.params.placeholder;

    if (!every) {
      log('INVALID_EVERY_X_EVENT', { event }, LogLevel.warn);
      return [];
    }

    if (fact.length === 0) {
      return [];
    }

    if (ommitLast && isPlaceholder(fact[fact.length - 1].type)) {
      fact = fact.slice(0, -1);
    }

    const extendedFacts: FactWithNeighbors[] = [];
    let nonPlaceholderIndex = 0;

    const factsWithState = fact.map(el => ({ ...el, canBeUsed: true }));

    for (let i = 0; i < factsWithState.length; i++) {
      const el = factsWithState[i];

      if (isPlaceholder(el.type)) {
        continue;
      }

      const factWithNeighboringPlaceholders: FactWithNeighbors = {
        ...el,
        [above]:
          i > 0 && isPlaceholder(factsWithState[i - 1].type) ? factsWithState[i - 1] : null,
        [under]:
          i < factsWithState.length - 1 && isPlaceholder(factsWithState[i + 1].type)
            ? factsWithState[i + 1]
            : null,
        nonPlaceholderIndex: ++nonPlaceholderIndex
      };

      extendedFacts.push(factWithNeighboringPlaceholders);
    }

    if (element?.length) {
      extendedFacts.forEach(ef => {
        ef.canBeUsed = element.includes(ef.type);
      });
    }

    if (exclude) {
      const { omittedFactNames, skipOverFactNames } = exclude;

      extendedFacts.forEach(ef => {
        if (omittedFactNames.includes(ef.type)) {
          if (ef[above]) {
            ef[above].canBeUsed = false;
          }
          if (ef[under]) {
            ef[under].canBeUsed = false;
          }
        }
        // TODO: remove "?" in AdsLayouts-Release-17
        if (skipOverFactNames?.includes(ef.type)) {
          ef.canBeUsed = false;
        }
      });
    }

    if (startIndex) {
      for (const ef of extendedFacts) {
        if (ef.nonPlaceholderIndex < startIndex) {
          ef.canBeUsed = false;
        } else {
          break;
        }
      }
    }

    const ActiveFacts: FactWithNeighbors[] = extendedFacts.filter(ef => ef.canBeUsed);
    let placeholders: PlaceholderTypeUsable[] = [];

    if (exclude && position) {
      let index = 0;
      const positionIsAbove = position === above;

      for (const ef of ActiveFacts) {
        if (++index < +every) {
          continue;
        }

        if (positionIsAbove && ef[above]?.canBeUsed) {
          placeholders.push(ef[above]);
          ef[above].canBeUsed = false;
          index = 0;
        } else if (ef[under]?.canBeUsed) {
          placeholders.push(ef[under]);
          ef[under].canBeUsed = false;
          index = 0;
        }
      }
    } else {
      const positionOrAbove = position ?? above;
      placeholders = ActiveFacts.filter((_, i) => (i + 1) % +every === 0)
        .map(el => el[positionOrAbove])
        .filter(el => el !== null);
    }

    // handle max placeholders selection
    if (!!max) {
      placeholders = placeholders.slice(0, +max);
    }

    return placeholders.map(({ id, type }) => ({ id, type }));
  }

  /**
   * Finds the nearest placeholder element based on the given event and facts.
   *
   * @param event The event object containing the placeholder information
   * @param fact The array of facts to search for the matching element
   * @returns An array of PlaceholderType representing the nearest placeholder element
   */
  getNearToElementPlaceholder(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { position, element } = event.params.placeholder;

    let matchingElementIndex: number | null = null;

    const elementArray: string[] = returnAsArrayEmpty(element);

    for (const el of elementArray) {
      const index = fact.findIndex(fel => fel?.type === el);
      if (index !== -1) {
        matchingElementIndex = index;
        break;
      }
    }
    if (matchingElementIndex === null) {
      return [];
    }

    const updatedIndex =
      position === PlaceholderPositionEnum.UNDER
        ? matchingElementIndex + 1
        : matchingElementIndex - 1;

    if (
      updatedIndex < 0 ||
      updatedIndex >= fact.length ||
      !isPlaceholder(fact[updatedIndex].type)
    ) {
      return [];
    }

    return [fact[updatedIndex]];
  }

  getPlaceholdersNearIndexes(event: IEvent, fact: FactType[]): PlaceholderType[] {
    const { position, element, elementsIndexes, countBackwards } = event.params.placeholder;

    if (!elementsIndexes?.length) {
      log('INVALID_EVENT_NEEDS_ELEMENTS_INDEXES', {}, LogLevel.warn);
      return [];
    }

    const facts = countBackwards ? cloneDeep(fact).reverse() : fact;

    const matchingElement = returnAsArrayEmpty(element).find(elementItem =>
      facts.some(({ type }) => type === elementItem)
    );

    const factWithIndex = facts.reduce((acc: PosFactType[], curr, index) => {
      if (curr?.type === matchingElement) {
        acc.push({
          ...curr,
          nonPlaceholderIndex: index
        });
      }
      return acc;
    }, []);

    const expectedPlaceholders: PlaceholderType[] = [];

    factWithIndex.forEach(({ nonPlaceholderIndex }, factIndex) => {
      if (elementsIndexes.includes(factIndex + 1)) {
        const updatedIndex =
          position === PlaceholderPositionEnum.UNDER
            ? nonPlaceholderIndex + 1
            : nonPlaceholderIndex - 1;

        if (
          updatedIndex >= 0 &&
          updatedIndex < facts.length &&
          isPlaceholder(facts[updatedIndex].type)
        ) {
          expectedPlaceholders.push(facts[updatedIndex]);
        }
      }
    });

    return expectedPlaceholders;
  }

  /**
   * Returns weight of given variant for given module type and device type.
   * If variant is unknown or fact doesn't have variant, returns 1.
   *
   * @param deviceType - desktop, tablet or smartphone
   * @param type - module type
   * @param variant - variant
   * @returns weight of given variant
   */
  getVariantWeight(
    deviceType: AdConfigDeviceTypeEnum,
    type: string,
    variant: string | undefined
  ): number {
    if (!variant) {
      return 1;
    }

    const moduleWithVariantOrNumber = this.variantWeights?.[deviceType]?.[type];

    if (typeof moduleWithVariantOrNumber === 'number') {
      return moduleWithVariantOrNumber;
    }

    const weight = moduleWithVariantOrNumber?.[variant];

    if (weight === undefined) {
      log('UNKNOWN_VARIANT', { deviceType, type, variant }, LogLevel.dev);

      return 1;
    }
    return weight;
  }
}
