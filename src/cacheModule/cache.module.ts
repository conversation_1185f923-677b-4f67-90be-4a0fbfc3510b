import { DynamicModule, Global, Module, Provider } from '@nestjs/common';
import { CACHE_MODULE_OPTIONS, CacheModule as NestJsCacheModule } from '@nestjs/cache-manager';
import { CacheService } from './cache.service';
import { ENV } from '../envalidConfig';
import { ModuleOptions } from 'InterfacesAndTypes';
import log from 'Logger/logger';
import { LogLevel } from 'ads-layouts-tools';

@Global()
@Module({})
export class CacheModule {
  static register(options: ModuleOptions): DynamicModule {
    const { isActive } = options;
    const providers: Provider[] = [];

    providers.push(CacheService);
    providers.push({
      provide: CACHE_MODULE_OPTIONS,
      useValue: { isActive }
    });

    const cacheParams = {
      isGlobal: true,
      ttl: isActive ? ENV.LOCAL_CACHE_TTL : 1,
      max: isActive ? ENV.LOCAL_CACHE_MAX_ITEMS_AMOUNT : 1,
      store: isActive ? 'memory' : undefined
    };

    log('CACHE_MODULE_REGISTER', { ...cacheParams }, LogLevel.info);

    const imports = [NestJsCacheModule.register(cacheParams)];

    return {
      module: CacheModule,
      imports,
      providers,
      exports: providers
    };
  }
}
