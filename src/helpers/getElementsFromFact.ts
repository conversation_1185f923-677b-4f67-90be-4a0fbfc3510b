import { CommonRequest, BodyElement } from 'InterfacesAndTypes';

/**
 * Retrieves elements of a fact of the specified type from a nested structure within a CommonRequest object.
 * Convenience function for tests, not meant to be used in production.
 * If null is returned, the function was used incorrectly.
 *
 * @param obj - The CommonRequest object containing a tree of elements to search through.
 * @param searchedType - The type of the fact to search for within the elements.
 * @returns The first matching BodyElement object found with the specified type.
 */
export const getElementFromRequest = (
  obj: CommonRequest,
  searchedType: string
): BodyElement => {
  const stack = [...obj.elements];

  while (stack.length) {
    const { id, type, elements } = stack.pop()!;

    if (type === searchedType) {
      return { id, type, elements };
    }

    if (elements) {
      stack.push(...elements);
    }
  }

  return { id: '', type: '', elements: [] };
};
