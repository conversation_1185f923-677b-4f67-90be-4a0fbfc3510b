import log from 'Logger/logger';
import { AccessModelEnum, PaywallEnum, LogLevel } from 'ads-layouts-tools';

export const validateAccessModel = () => {
  const allowed = Object.values(AccessModelEnum);

  return (passedValue: string) => {
    const passedValueIsNotAllowed = !allowed.includes(passedValue as any);

    if (passedValueIsNotAllowed) {
      log('ACCESS_MODEL_VALIDATION_FAILED', { passedValue }, LogLevel.warn);
    }

    return passedValue;
  };
};

export const validatePaywall = () => {
  const allowed = Object.values(PaywallEnum);

  return (passedValue: string) => {
    const passedValueIsNotAllowed = !allowed.includes(passedValue as any);

    if (passedValueIsNotAllowed) {
      log('PAYWALL_VALIDATION_FAILED', { passedValue }, LogLevel.warn);
    }

    return passedValue;
  };
};
