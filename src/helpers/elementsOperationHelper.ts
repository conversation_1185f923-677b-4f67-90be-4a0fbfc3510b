import { FactType, PlaceholderEnum, PlaceholderType } from 'InterfacesAndTypes';

/**
 * Checks if a given string is a valid value of the PlaceholderEnum.
 *
 * This is a type guard which can be used to narrow the type of a string from
 * string to PlaceholderEnum. It returns true if the value is a valid value of
 * PlaceholderEnum, and false otherwise.
 *
 * @param value The value to check.
 * @returns true if the value is a valid value of PlaceholderEnum, and false
 * otherwise.
 */
export const isPlaceholder = (value: string): value is PlaceholderEnum =>
  Object.values(PlaceholderEnum).includes(value as PlaceholderEnum);

/**
 * Returns an array of all placeholders in the given fact array.
 *
 * @param fact - The fact array to extract placeholders from. Can be undefined.
 * @returns An array of all placeholders in the given fact array. Returns an empty array if the input array is undefined.
 */
export const getPlaceholdersFromFact = (fact: FactType[]): PlaceholderType[] =>
  fact?.filter(el => isPlaceholder(el.type)) ?? [];

/**
 * Counts the number of facts with a given type in a given fact array.
 *
 * @param fact - The fact array to count in. Can be undefined.
 * @param type - The type of fact to count.
 * @returns The number of facts with the given type in the given fact array.
 */
export const countFactInContainingFact = <T extends { type: string }>(
  fact: T[] | undefined,
  type: string
): number => (fact?.filter(el => el.type === type) ?? []).length;
