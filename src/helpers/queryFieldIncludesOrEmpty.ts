import { Rule } from 'ads-layouts-tools';
import { KeysWithArrayValues } from 'InterfacesAndTypes';

/**
 * Generates a mongoose part of a query object for a field.
 *
 * @param fieldName - The name of the field to query.
 * @param valueToSearchFor - A comma-separated list of values to look for in the field of the document.
 * @returns A mongoose query object.
 */
export const queryFieldIncludesOrEmpty = (
  fieldName: KeysWithArrayValues<Rule>,
  valueToSearchFor: string
) => {
  const arr = valueToSearchFor.split(',');
  return {
    $or: [
      { [fieldName]: { $elemMatch: { $in: arr } } },
      { [fieldName]: { $size: 0 } },
      { [fieldName]: { $exists: false } }
    ]
  };
};
