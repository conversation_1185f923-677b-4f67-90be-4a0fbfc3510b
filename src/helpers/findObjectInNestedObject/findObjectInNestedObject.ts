export const findObjectInNestedObject = (
  inputObject: object,
  fieldToFind: string,
  keyToFind: string,
  extractField: string
): object | null => {
  let foundObj: object | null = null;
  JSON.stringify(inputObject, (_, nestedValue) => {
    if (nestedValue && nestedValue[fieldToFind] === keyToFind) {
      foundObj = nestedValue[extractField];
    }
    return nestedValue;
  });
  return foundObj;
};
