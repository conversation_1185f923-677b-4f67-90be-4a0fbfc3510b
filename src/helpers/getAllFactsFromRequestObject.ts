import { CommonRequest, PlaceholderEnum } from 'InterfacesAndTypes';

export const getAllFactsFromRequestObject = (inputObject: CommonRequest): string[] => {
  const nestedFacts: string[] = [];
  const firstLevelFacts = Object.keys(inputObject).filter(k => !['type', 'meta'].includes(k));
  const noFactValues = [...Object.values(PlaceholderEnum), 'paragraph', 'teaser', undefined];

  JSON.stringify(inputObject, (_, nestedValue) => {
    if (nestedValue) {
      const nestedType = nestedValue['type'];

      if (nestedValue && !noFactValues.includes(nestedType)) {
        nestedFacts.push(nestedType);
      }

      return nestedValue;
    }
  });
  return [...firstLevelFacts, ...nestedFacts];
};
