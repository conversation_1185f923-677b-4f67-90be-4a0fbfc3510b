import { Modu<PERSON> } from '@nestjs/common';
import { AppCacheController } from './appCache.controller';
import { DisplayConfigModule } from '../displayConfig/displayConfig.module';
import { WorkersCacheConfig, WorkersCacheConfigSchema } from 'ads-layouts-tools';
import { AppCacheService } from './appCache.service';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    DisplayConfigModule,
    MongooseModule.forFeature([
      {
        name: WorkersCacheConfig.name,
        schema: WorkersCacheConfigSchema
      }
    ])
  ],
  controllers: [AppCacheController],
  providers: [AppCacheService]
})
export class AppCacheModule {}
