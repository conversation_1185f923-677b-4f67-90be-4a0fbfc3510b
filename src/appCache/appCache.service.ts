import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorkersCacheConfig, WorkersCacheConfigDocument } from 'ads-layouts-tools';

@Injectable()
export class AppCacheService {
  constructor(
    @InjectModel(WorkersCacheConfig.name)
    private workersCacheConfigModel: Model<WorkersCacheConfigDocument>
  ) {}

  getWorkerCacheConfig = async (): Promise<WorkersCacheConfigDocument[]> => {
    const workersCacheConfigs = await this.workersCacheConfigModel
      .find({})
      .select('-createdAt -updatedAt -_id -__v');

    return workersCacheConfigs;
  };
}
