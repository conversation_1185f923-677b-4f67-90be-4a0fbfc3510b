import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdConfigService } from './adConfig.service';
import { AdConfig, AdConfigSchema } from 'ads-layouts-tools';
import { ExtensionModule } from '../extensionConfig/extension.module';

@Module({
  imports: [
    ExtensionModule,
    MongooseModule.forFeature([
      {
        name: AdConfig.name,
        schema: AdConfigSchema
      }
    ])
  ],
  providers: [AdConfigService],
  exports: [AdConfigService]
})
export class AdConfigModule {}
