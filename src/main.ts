import 'Logger/tracer';

import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { MasterModule, SlaveModule } from './app.module';
import { initSwagger } from './initSwagger';
import log from 'Logger/logger';
import { JoiPipe } from 'nestjs-joi';
import { cache } from './localCache/Cache';
import { ENV } from './envalidConfig';
import * as os from 'os';
import { LogLevel, ServiceEnvEnum } from 'ads-layouts-tools';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const cluster = require('cluster');
import {
  MongoExceptionFilter,
  HttpExceptionFilter,
  MongooseValidatorExceptionFilter
} from './errors/exceptionFilters';
import helmet from '@fastify/helmet';

async function bootstrap() {
  if (cluster.isPrimary) {
    process.env.UNIQUE_PROCESS_ID = `master_${os.hostname()}_${os.userInfo().uid}_${os.userInfo().gid}_${process.pid}`;
    log('UNIQUE_WORKER_PROCESS_ID', { uniqueProcessId: process.env.UNIQUE_PROCESS_ID });

    log('RUN_MASTER_PROCESS', {}, LogLevel.dev);

    const numCPUs =
      ENV.APP_ENV == ServiceEnvEnum.LOCAL ? 1 : os.cpus().length - ENV.SLAVE_COUNT_REDUCE;
    for (let i = 0; i < numCPUs; i++) {
      cluster.fork();
    }

    cluster.on('exit', () => {
      log('ERROR_WORKER_DIED', {}, LogLevel.error);
      cluster.fork();
    });

    const masterFastifyAdapter = new FastifyAdapter({
      bodyLimit: ENV.BODY_LIMIT_MB
    });

    log('APP_CACHE_INIT', { persistentStore: cache /* without TTL */ });

    const masterApp = await NestFactory.create<NestFastifyApplication>(
      MasterModule,
      masterFastifyAdapter
    );

    masterApp.register(helmet);

    initSwagger(masterApp);

    masterApp.useGlobalPipes(new JoiPipe());
    masterApp.useGlobalFilters(
      new MongoExceptionFilter(),
      new HttpExceptionFilter(),
      new MongooseValidatorExceptionFilter()
    );

    const port = ENV.APP_PORT;
    log('APP_PRIMARY_STARTED', { port });

    await masterApp.listen(port, ENV.APP_ADDRESS);
  } else if (cluster.isWorker) {
    process.env.UNIQUE_PROCESS_ID = `master_${os.hostname()}_${os.userInfo().uid}_${os.userInfo().gid}_${process.pid}`;

    log('UNIQUE_WORKER_PROCESS_ID', { uniqueProcessId: process.env.UNIQUE_PROCESS_ID });
    log('APP_CACHE_INIT', { persistentStore: cache /* without TTL */ });

    const app = await NestFactory.create<NestFastifyApplication>(
      SlaveModule,
      new FastifyAdapter()
    );

    app.register(helmet);

    initSwagger(app);

    app.useGlobalPipes(new JoiPipe());
    app.useGlobalFilters(
      new MongoExceptionFilter(),
      new HttpExceptionFilter(),
      new MongooseValidatorExceptionFilter()
    );

    const port = ENV.APP_PORT_SLAVE;
    log(`APP_WORKER_${cluster.worker.id}_STARTED`, { port });

    await app.listen(port, ENV.APP_ADDRESS);
  }
}
bootstrap();
