import { FactBase } from 'InterfacesAndTypes';

/**
 * Evaluates whether a specified fact element is at the expected position
 * within a filtered list of elements, considering exclusion and uniqueness rules.
 *
 * @param factElements - The array of elements to check against, which may be undefined.
 * @param ruleConditionParams - An object containing rule parameters:
 *   - expectedFact: The type of the fact to be checked.
 *   - position: The expected position (1-based index) of the fact type.
 *   - excludedFacts: An array of fact types to be excluded from consideration.
 *   - expectOneFactOnly: A boolean indicating if exactly one instance of
 *     the expected fact type is allowed.
 * @returns A boolean indicating if the conditions are met: the expected fact type
 * is at the specified position, considering exclusions, and uniqueness as specified.
 */
export const expectedPositionOperator = (
  factElements: FactBase[] | undefined,
  ruleConditionParams: {
    expectedFact: string;
    position: number;
    excludedFacts: string[];
    expectOneFactOnly: boolean;
  }
): boolean => {
  const { expectedFact, position, excludedFacts, expectOneFactOnly } = ruleConditionParams;

  const factWithoutExcludedFacts =
    factElements?.filter(el => !excludedFacts.includes(el.type)) ?? [];

  if (expectOneFactOnly) {
    const positionOfExpectedFact =
      factWithoutExcludedFacts.findIndex(el => el.type === expectedFact) + 1;

    return (
      positionOfExpectedFact === position &&
      factWithoutExcludedFacts.filter(el => el.type === expectedFact).length === 1
    );
  } else {
    const positionsOfExpectedFact: number[] = factWithoutExcludedFacts.reduce(
      (acc: number[], curr, index: number) => {
        if (curr.type === expectedFact) {
          acc.push(++index);
        }
        return acc;
      },
      []
    );

    return positionsOfExpectedFact.includes(position);
  }
};
