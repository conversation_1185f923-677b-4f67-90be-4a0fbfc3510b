import { countFactInContainingFact } from 'Helpers';
import { FactBase } from 'InterfacesAndTypes';

export const containsOfTypeOperator = (
  factValue: FactBase[] | undefined,
  ruleConditionParams: { count: number; type: string; rule: string }
): boolean => {
  const { count, type, rule } = ruleConditionParams;
  const countInt = +count;

  // Return false for negative count values
  if (countInt < 0) {
    return false;
  }

  const factsCount = countFactInContainingFact(factValue, type);

  switch (rule) {
    case 'equal':
      return factsCount === countInt;
    case 'lessThan':
      return factsCount < countInt;
    case 'lessOrEqualTo':
      return factsCount <= countInt;
    case 'moreThan':
      return factsCount > countInt;
    case 'moreOrEqualTo':
    default:
      return factsCount >= countInt;
  }
};
