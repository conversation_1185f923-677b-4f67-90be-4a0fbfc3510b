import { createHeadersWithValuesSchema } from 'Helpers';
import { CustomHttpStatus, LogLevel } from 'ads-layouts-tools';
import * as joi from 'joi';
import log from 'Logger/logger';
import { CreateException } from '../../errors/exceptions';
import { BodyToHeaderParse, ContentMeta } from 'InterfacesAndTypes';

export const runHeadersValidation = (
  bodyMeta: ContentMeta,
  reqHeaders: BodyToHeaderParse<ContentMeta>
): void => {
  const result = joi
    .object<BodyToHeaderParse<ContentMeta>>(createHeadersWithValuesSchema(bodyMeta))
    .options({ abortEarly: false, allowUnknown: true })
    .validate(reqHeaders);

  if (result.error) {
    const errorMessages = result.error.details.map(d => d.message).join(', ');

    log('ERROR_HEADERS_VALIDATION_ERROR', { errorMessages }, LogLevel.error);

    throw CreateException({
      message: `Head<PERSON> validation error. ${errorMessages}`,
      statusCode: CustomHttpStatus.HEADERS_VALIDATION
    });
  }

  log('REQUEST_HEADERS_VALIDATION_PASSED', { headersValid: true }, LogLevel.dev);
};
