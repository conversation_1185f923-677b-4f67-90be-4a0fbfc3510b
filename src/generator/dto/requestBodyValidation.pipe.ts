import { PipeTransform, HttpStatus, Injectable } from '@nestjs/common';
import log from 'Logger/logger';
import { ServiceEnvEnum, AdConfigDeviceTypeEnum, LogLevel } from 'ads-layouts-tools';
import * as <PERSON><PERSON> from 'joi';
import { CacheService } from '../../cacheModule/cache.service';
import { ENV } from '../../envalidConfig';
import { CreateException } from '../../errors/exceptions';
import {
  requireStringValues,
  returnAsArrayEmpty,
  validateAccessModel,
  validatePaywall
} from 'Helpers';
import { CommonRequest, ContentMeta, ContentMetaRequiredFields } from 'InterfacesAndTypes';

@Injectable()
export class RequestBodyValidationPipe implements PipeTransform {
  constructor(private readonly cache: CacheService) {}

  public async transform(value: CommonRequest): Promise<CommonRequest> {
    const serviceIdArrayValidationEnv = ENV.SERVICE_ID_ARRAY_VALIDATION;
    let validServiceIds: string[] = [];

    if (serviceIdArrayValidationEnv === 'ENABLED') {
      const validServiceIdsEnv = ENV.VALID_SERVICE_IDS;
      const localCacheServiceIds = await this.cache.getServiceIds();

      validServiceIds = localCacheServiceIds ?? returnAsArrayEmpty(validServiceIdsEnv);
    }

    if (!value) {
      log('ERROR_GENERATOR_NO_BODY_CONTENT', { value }, LogLevel.error);
      throw CreateException({
        message: 'Request Body is required',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const result = Joi.object<CommonRequest>({
      ...requireStringValues<CommonRequest>(['type']),
      direction: Joi.string(),
      meta: Joi.object<ContentMeta>({
        ...requireStringValues<ContentMetaRequiredFields>([
          'locationInfoSectionId',
          'locationInfoSectionName',
          'locationInfoPageId',
          'siteVersionIdentifier',
          'time'
        ]),
        locationInfoPageId: Joi.string().allow('', null).required(),
        accessModel: Joi.string().custom(validateAccessModel()),
        paywall: Joi.string().custom(validatePaywall()),
        siteVersion: Joi.string().allow(''),
        serviceId: Joi.string()
          .valid(...validServiceIds)
          .allow('')
          .required(),
        serviceEnv: Joi.alternatives()
          .try(
            Joi.string().valid(...Object.values(ServiceEnvEnum)),
            Joi.string().regex(new RegExp(`^${ServiceEnvEnum.STAGE}\\d+$`)),
            Joi.string().regex(/^prev-.*/)
          )
          .error(errors => {
            const defaultServiceEnv = Object.values(ServiceEnvEnum).join(', ');
            errors[0].message = `serviceEnv should be one of <${defaultServiceEnv}> or match pattern 'prev-*', or match pattern '^${ServiceEnvEnum.STAGE}\\d+$'`;

            return errors[0];
          }),
        deviceType: Joi.string()
          .valid(...Object.values(AdConfigDeviceTypeEnum))
          .required(),
        rulesPackage: Joi.string()
      }).required(),
      elements: Joi.array().required()
    })
      .options({
        abortEarly: false
      })
      .validate(value);

    if (result.error) {
      const errorMessages = result.error?.details?.map(d => d.message).join();

      log('ERROR_BODY_VALIDATION_ERROR', { err: errorMessages }, LogLevel.error);

      throw CreateException({
        message: `Body validation error. Errors: ${errorMessages}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    log('REQUEST_BODY_VALIDATION_PASSED', { bodyValid: true }, LogLevel.dev);

    return value;
  }
}
