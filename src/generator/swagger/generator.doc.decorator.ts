import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiHeader, ApiProduces, ApiBody } from '@nestjs/swagger';
import { CommonRequestModel, GeneratorResponseModel } from '../../swaggerModel/swaggerModels';

export function GetGenerateDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiBody({
      type: CommonRequestModel
    }),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: GeneratorResponseModel
    }),
    ApiResponse({ status: 400, description: 'Bad request' }),
    ApiResponse({ status: 500, description: 'Server error' }),
    ApiHeader({
      name: 'X-AT-service-id',
      required: true,
      description: 'service id'
    }),
    ApiHeader({
      name: 'X-AT-site-version',
      required: false,
      description: 'site version identifier'
    }),
    ApiHeader({
      name: 'X-AT-service-env',
      required: false,
      description: 'site env'
    }),
    A<PERSON><PERSON>ead<PERSON>({
      name: 'X-AT-device-type',
      required: true,
      description: 'device type'
    }),
    ApiHeader({
      name: 'X-AT-location-info-page-type',
      required: true,
      description: 'location page type'
    }),
    ApiHeader({
      name: 'X-AT-location-info-section-id',
      required: true,
      description: 'location section id'
    }),
    ApiHeader({
      name: 'X-AT-location-info-section-name',
      required: true,
      description: 'location section name'
    }),
    ApiHeader({
      name: 'X-AT-location-info-page-id',
      required: true,
      description: 'location page id'
    }),
    ApiHeader({
      name: 'X-AT-site-version-identifier',
      required: true,
      description: 'version identifier'
    })
  );
}
