import { Module } from '@nestjs/common';
import { GeneratorController } from './generator.controller';
import { GeneratorService } from './generator.service';
import { RulesModule } from '../rules/rules.module';
import { AdConfigModule } from '../adConfigs/adConfig.module';
import { DisplayConfigModule } from '../displayConfig/displayConfig.module';
import { ServiceToPackageMapModule } from '../serviceToPackageMap/serviceToPackageMap.module';
import { EventsModule } from '../events/events.module';

@Module({
  imports: [
    RulesModule,
    AdConfigModule,
    DisplayConfigModule,
    ServiceToPackageMapModule,
    EventsModule
  ],
  controllers: [GeneratorController],
  providers: [GeneratorService]
})
export class GeneratorModule {}
