import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  Conditions,
  ConditionsSchema,
  Event,
  EventSchema,
  Rule,
  RuleSchema
} from 'ads-layouts-tools';
import { RuleService } from './rules.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Rule.name,
        schema: RuleSchema
      },
      {
        name: Event.name,
        schema: EventSchema
      },
      {
        name: Conditions.name,
        schema: ConditionsSchema,
        discriminators: [
          { name: AnyConditions.name, schema: AnyConditionsSchema },
          { name: AllConditions.name, schema: AllConditionsSchema }
        ]
      }
    ])
  ],
  providers: [RuleService],
  exports: [RuleService]
})
export class RulesModule {}
