import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  CustomHttpStatus,
  LogLevel,
  Rule,
  RuleDocument
} from 'ads-layouts-tools';
import { queryFieldIncludesOrEmpty } from 'Helpers';
import { RulesKey, RulesQueryArgs } from 'InterfacesAndTypes';
import log from 'Logger/logger';
import { FilterQuery, Model } from 'mongoose';
import { CacheService } from '../cacheModule/cache.service';
import { CreateException } from '../errors/exceptions';

@Injectable()
export class RuleService {
  constructor(
    @InjectModel(Rule.name) private ruleModel: Model<RuleDocument>,
    private readonly cache: CacheService
  ) {}

  private getCacheKey(args: RulesQueryArgs): RulesKey {
    const {
      rulesPackage,
      contentMeta: {
        serviceId,
        deviceType,
        locationInfoPageType,
        paywall,
        accessModel,
        siteVersion
      }
    } = args;

    const keyParts = [CachePartsEnum.RULES, serviceId, locationInfoPageType, deviceType];

    [rulesPackage, paywall, accessModel, siteVersion].forEach(part => {
      if (part) {
        keyParts.push(part);
      }
    });

    return keyParts.join('__') as RulesKey;
  }

  private prepareQuery(args: RulesQueryArgs): FilterQuery<Rule> {
    const {
      rulesPackage,
      type,
      contentMeta: {
        serviceId,
        deviceType,
        locationInfoPageType,
        locationInfoPageId,
        locationInfoSectionId,
        siteVersion,
        accessModel,
        paywall
      }
    } = args;

    const queryParts: FilterQuery<Rule>[] = [
      { rulesPackage: { $eq: rulesPackage } },
      { pageType: { $in: type } },
      { layout: { $in: deviceType } },
      { enabled: { $eq: true } },
      queryFieldIncludesOrEmpty('pageId', locationInfoPageId ?? ''),
      queryFieldIncludesOrEmpty('sectionId', locationInfoSectionId)
    ];

    if (!rulesPackage) {
      queryParts.push({ serviceId: { $in: serviceId } });
    }

    if (locationInfoPageType !== undefined) {
      queryParts.push(queryFieldIncludesOrEmpty('locationInfoPageType', locationInfoPageType));
    }

    if (siteVersion !== undefined) {
      queryParts.push(queryFieldIncludesOrEmpty('siteVersion', siteVersion));
    }

    if (accessModel !== undefined) {
      queryParts.push(queryFieldIncludesOrEmpty('accessModel', accessModel));
    }

    if (paywall !== undefined) {
      queryParts.push(queryFieldIncludesOrEmpty('paywall', paywall));
    }

    return { $and: queryParts };
  }

  async getPageRules(args: RulesQueryArgs, omitCache = false): Promise<RuleDocument[]> {
    const key = this.getCacheKey(args);

    let ruleDocuments = await this.cache.get(key, omitCache);

    if (!ruleDocuments) {
      const query = this.prepareQuery(args);

      ruleDocuments = await this.ruleModel
        .find(query)
        .select('-createdAt -updatedAt -_id -__v')
        .populate({
          path: 'event adConfig conditions',
          select: '-createdAt -updatedAt -_id -__v -all._id -any._id -name -desc',
          strictPopulate: false
        })
        .exec();

      if (ruleDocuments?.length > 0) {
        await this.cache.set(key, ruleDocuments, omitCache);
      }
    }

    log('ALL_RULES_DATA_LENGTH', { ruleData: ruleDocuments.length }, LogLevel.dev);

    if (!ruleDocuments || ruleDocuments.length === 0) {
      throw this.noRulesFoundException(args);
    }

    return ruleDocuments;
  }

  private noRulesFoundException(args: RulesQueryArgs) {
    const {
      rulesPackage,
      type,
      contentMeta: {
        serviceId,
        deviceType,
        locationInfoPageType,
        locationInfoPageId,
        locationInfoSectionId,
        siteVersion,
        accessModel,
        paywall
      }
    } = args;

    const statusCode = CustomHttpStatus.CANNOT_FIND_ANY_RULES;

    log(
      `WARN_${statusCode}_CANNOT_FIND_ANY_RULES`,
      {
        rulesPackage,
        type,
        serviceId,
        deviceType,
        locationInfoPageType,
        locationInfoSectionId,
        locationInfoPageId,
        siteVersion,
        accessModel,
        paywall
      },
      LogLevel.warn
    );
    const message = `Cannot get any rules data for rulesPackage: ${rulesPackage ?? 'original rules'}, type: ${type}, serviceId: ${serviceId}, deviceType: ${deviceType}, sectionId: ${locationInfoSectionId}, pageId: ${locationInfoPageId}, pageType: ${locationInfoPageType}, siteVersion: ${siteVersion}, accessModel: ${accessModel}, paywall: ${paywall}`;

    return CreateException({ message, statusCode });
  }
}
