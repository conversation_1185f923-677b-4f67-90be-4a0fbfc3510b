import { configureLogger } from 'ads-layouts-tools';
import * as logger from 'node-color-log';
import { ENV } from '../envalidConfig';
import { tracerUtils } from './trace';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const cluster = require('cluster');
const clusterID = cluster.isPrimary ? 'PRIMARY' : cluster.worker?.id.toString();

const log = configureLogger(logger, ENV, tracerUtils, clusterID);

export default log;
