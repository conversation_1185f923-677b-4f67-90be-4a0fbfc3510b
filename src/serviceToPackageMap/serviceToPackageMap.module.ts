import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ServiceToPackageMap, ServiceToPackageMapSchema } from 'ads-layouts-tools';
import { ServiceToPackageMapService } from './serviceToPackageMap.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ServiceToPackageMap.name,
        schema: ServiceToPackageMapSchema
      }
    ])
  ],
  providers: [ServiceToPackageMapService],
  exports: [ServiceToPackageMapService]
})
export class ServiceToPackageMapModule {}
