import { HttpStatus, Injectable } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerException, ThrottlerRequest } from '@nestjs/throttler';
import log from 'Logger/logger';
import { LogLevel } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { CreateException } from '../errors/exceptions';

@Injectable()
export class LoggingThrottlerGuard extends ThrottlerGuard {
  async handleRequest(request: ThrottlerRequest): Promise<boolean> {
    try {
      return await super.handleRequest(request);
    } catch (error) {
      if (error instanceof ThrottlerException) {
        const { context } = request;
        const req = context.switchToHttp().getRequest<FastifyRequest>();

        log(
          'TOO_MANY_REQUESTS',
          {
            IP: req.ip,
            Path: req.url,
            Headers: JSON.stringify(req.headers)
          },
          LogLevel.dev
        );

        throw CreateException({
          message: 'Too Many Requests',
          statusCode: HttpStatus.TOO_MANY_REQUESTS
        });
      }

      log('THROTTLE_GUARD_UNKNOWN_ERROR', { error }, LogLevel.error);
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }
}
