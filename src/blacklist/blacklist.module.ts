import { Module } from '@nestjs/common';
import { BlacklistController } from './blacklist.controller';
import { ThrottlerModule } from '@nestjs/throttler';
import { ENV } from '../envalidConfig';

@Module({
  imports: [
    ThrottlerModule.forRoot([
      {
        ttl: ENV.THROTTLE_TTL,
        limit: ENV.THROTTLE_LIMIT,
        blockDuration: ENV.THROTTLE_BLOCK_DURATION
      }
    ])
  ],
  controllers: [BlacklistController]
})
export class BlacklistModule {}
