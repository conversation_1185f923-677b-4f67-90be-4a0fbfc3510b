import { Controller, HttpStatus, All, UseGuards, Req } from '@nestjs/common';
import { CreateException } from '../errors/exceptions';
import { LoggingThrottlerGuard } from './customThrottlerGuard.service';
import { FastifyRequest } from 'fastify';
import log from 'Logger/logger';
import { LogLevel } from 'ads-layouts-tools';

@Controller('/*')
export class BlacklistController {
  @UseGuards(LoggingThrottlerGuard)
  @All()
  allNonExplicitEndpoints(@Req() req: FastifyRequest) {
    log(
      'FORBIDDEN_PATH',
      {
        IP: req.ip,
        Path: req.url,
        Headers: JSON.stringify(req.headers)
      },
      LogLevel.dev
    );

    throw CreateException({
      message: 'Forbidden path',
      statusCode: HttpStatus.FORBIDDEN
    });
  }
}
