import {
  between0And100,
  between0AndNumCPUs,
  days,
  megaBytes,
  minutes,
  seconds,
  ServiceEnvEnum,
  ServiceName
} from 'ads-layouts-tools';
import * as dotenv from 'dotenv';
import { cleanEnv, host, num, port, str, url } from 'envalid';
dotenv.config();

export const ENV = cleanEnv(process.env, {
  CACHE_MAX_SIZE: num({ default: 4000 }),
  CACHE_SIZE_DAYS: days({ default: 2 }),

  // MONGO
  MONGO_HOST: url({ default: 'mongodb://localhost' }),
  MONGO_DB_NAME: str({ default: 'adsLayouts' }),
  MONGO_PASS: str({ default: '' }),
  MONGO_USER: str({ default: '' }),

  //APP
  APP_PORT: port({ default: 4002 }),
  APP_PORT_SLAVE: port({ default: 4001 }),
  APP_ADDRESS: host({ default: '0.0.0.0' }),
  SERVICE_NAME: str<ServiceName>({ default: ServiceName.WORKER }),
  LOG_FILTERED_PLACEHOLDERS: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),

  CACHE_CONTROL: str({ default: 'public,max-age=1,s-maxage=1' }),

  LOCAL_CACHE_MAX_ITEMS_AMOUNT: num({ default: 50000 }),
  LOCAL_CACHE_TTL: minutes({ default: 60 }),
  LOCAL_CACHE_RESET_CHECK: minutes({ default: 2 }),
  LOCAL_CACHE_EXTENSION_TTL: minutes({ default: 5 }),
  LOCAL_CACHE_VARIANT_WEIGHTS_TTL: minutes({ default: 60 }),

  APP_ENV: str({
    default: ServiceEnvEnum.LOCAL,
    choices: Object.values(ServiceEnvEnum)
  }),
  HEADERS_VALIDATION: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),
  APP_CACHE: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),

  DISPLAY_CONFIG_URL: url({
    default: 'https://display-at.cdntvn.pl/lambdaDisplayConfigs/config.json'
  }),
  VALID_SERVICE_IDS: str({
    default:
      'news,19,player,vod,ddtvn,fakty,mask_singer,czt,tvn,tvn24,warszawa,tvnmeteo,biznes,40_kontra_20,agent_gwiazdy,brzydula,bunt,chylka,domowe_rewolucje,dorota_inspiruje,hotel_paradise,kobieta_na_krancu_swiata,lab,lego_masters,mam_talent,motyw,nieobecni,one_night_squad,papiery_na_szczescie,power_couple,projekt_lady,przez_atlantyk,receptura,rowni_sobie,skazana,slub_od_pierwszego_wejrzenia,szadz,szostka,tajemnica_zawodowa,top_model,totalne_remonty_szelagowskiej,weekendowa_metamorfoza,zony_miami,zywioly_saszy,konkret24,herkules,moj_agent,perfect_picture,playerlove,kto_odmowi_pannie_mlodej,na_wspolnej,kuba,kuchenne_rewolucje,master_chef_junior,ameryka_express,masterchef,milionerzy,bigbrother,projekt_plaza,projekt_zima,ugotowani,wielki_mecz,uwaga,true_love,nastolatki_rzadza,kto_to_wie,sekrety_zycia,pati,superwizjer,39_i_pol_tygodnia,jestem_soba,kod_genetyczny,krolestwo_kobiet,my_way,starsza_pani_musi_fiknac,ttv,tvn_fabula,tvn_style,tvn_turbo,tvn_siedem,metro,hgtv,travel_channel,tlc,discovery_channel,itvn,itvn_extra,foodnetwork,lipowo,behawiorysta,detektywi,wbd,efekt_domina,go,odwroceni_ojcowie_i_corki,pod_powierzchnia,pulapka,usta_usta,zakochani_po_uszy,zdrowietvn'
  }),

  SERVICE_ID_ARRAY_VALIDATION: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),

  UPDATE_SERVICE_ID_INTERVAL: minutes({ default: 30 }),
  UPDATE_SERVICE_ID_TTL: minutes({ default: 31 }),

  BODY_LIMIT_MB: megaBytes({ default: 50 }),

  HEALTH_CHECK_LIMIT: between0And100({ default: 50 }),

  SEND_DEV_LOGS_TO_DATADOG: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),
  SEND_CACHE_LOGS_TO_DATADOG: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),

  SLAVE_COUNT_REDUCE: between0AndNumCPUs({ default: 0 }),

  THROTTLE_TTL: seconds({ default: 60 }),
  THROTTLE_LIMIT: num({ default: 10 }),
  THROTTLE_BLOCK_DURATION: seconds({ default: 60 })
});
