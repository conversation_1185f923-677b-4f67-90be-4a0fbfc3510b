{
  "parser": "@typescript-eslint/parser",
  "env": {
    "es2022": true,
    "node": true
  },
  "extends": [
    "plugin:@typescript-eslint/recommended",
    // "plugin:@typescript-eslint/recommended-requiring-type-checking",
    "plugin:prettier/recommended"
  ],
  "parserOptions": {
    "project": "tsconfig.json",
    "ecmaVersion": 2022,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "plugins": ["@typescript-eslint/eslint-plugin", "unused-imports"],
  "rules": {
    "prettier/prettier": ["error", {}, { "usePrettierrc": true }],
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "typescript-eslint/no-unsafe-assignment": "off",
    "typescript-eslint/no-unsafe-argument": "off",
    "typescript-eslint/no-unsafe-return": "off",
    "typescript-eslint/no-unsafe-member-access": "off",
    "typescript-eslint/no-unsafe-call": "off",
    "@typescript-eslint/no-empty-function": "warn",
    "@typescript-eslint/no-use-before-define": "warn",
    "@typescript-eslint/no-inferrable-types": "warn",
    "@typescript-eslint/ban-types": "warn",
    "@typescript-eslint/naming-convention": [
      "off",
      {
        "selector": "interface",
        "format": ["PascalCase"],
        "prefix": ["I"]
      }
    ],
    "no-console": "warn",
    "unused-imports/no-unused-imports": "error",
    "no-inner-declarations": "warn",
    "no-plusplus": "off",
    "no-await-in-loop": "off",
    "prefer-destructuring": [
      "warn",
      {
        "array": true,
        "object": true
      },
      {
        "enforceForRenamedProperties": false
      }
    ]
  }
}
