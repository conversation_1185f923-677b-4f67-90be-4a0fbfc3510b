import { isIP } from 'net';

const existy = (value: unknown): value is NonNullable<typeof value> => !!value;
const isString = (value: unknown): value is string =>
  existy(value) && typeof value === 'string';
const isObject = (value: unknown): value is object =>
  Object.prototype.toString.call(value) === '[object Object]';
const isValidIp = (value: unknown): value is string => isString(value) && isIP(value) !== 0;

export const is = {
  existy,
  ip: isValidIp,
  object: isObject,
  string: isString
};
