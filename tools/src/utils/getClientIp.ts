import { FastifyRequest } from 'fastify';
import { is } from './is';

const listOfPossibleIPHeaders = [
  'x-client-ip',
  'x-forwarded-for',
  'cf-connecting-ip',
  'fastly-client-ip',
  'true-client-ip',
  'x-real-ip',
  'x-cluster-client-ip',
  'x-forwarded',
  'forwarded-for',
  'forwarded',
  'x-appengine-user-ip',
  'Cf-Pseudo-IPv4'
];

const transformToString = (ips: string | string[] | null | undefined): string => {
  return Array.isArray(ips) ? ips.join(',') : (ips ?? '');
};

function findIp(headerValue?: string | string[]): string | undefined {
  if (!headerValue) {
    return;
  }

  const headerAsString = transformToString(headerValue);

  const ips = headerAsString.split(',').map(headerPart => {
    const ip = headerPart.trim();

    if (ip.includes(':')) {
      const splitted = ip.split(':');

      if (splitted.length === 2) {
        // IPv4
        return splitted[0];
      }
    }

    return ip;
  });

  return ips.find(ip => is.ip(ip));
}

export const getClientIp = (req: FastifyRequest | FastifyRequest['raw']): string => {
  if (is.ip(req.socket.remoteAddress)) {
    return req.socket.remoteAddress;
  }

  if (req.headers) {
    for (const header of listOfPossibleIPHeaders) {
      const clientIp = findIp(req.headers[header]);

      if (clientIp) {
        return clientIp;
      }
    }
  }

  if ('raw' in req) {
    return getClientIp(req.raw);
  }

  return 'unknown';
};
