import { CustomHttpStatus, ErrorInterface } from '../types';

export const isErrorInterface = (error: unknown): error is ErrorInterface =>
  typeof error === 'object' && error !== null && 'statusCode' in error && 'message' in error;

const setupError2XXhelper = () => {
  const all2XXcodes = Object.values(CustomHttpStatus)
    .filter(value => typeof value === 'number')
    .filter(value => value >= 200 && value < 300);

  const max2XXCode = Math.max(...all2XXcodes);
  const min2XXCode = Math.min(...all2XXcodes);

  return (statusCode: number | undefined) =>
    statusCode === undefined || statusCode < min2XXCode || statusCode > max2XXCode;
};

/**
 * Helper function to check if a status code is not in 209-299 range.
 *
 * Used in Worker.
 *
 * If the status code is undefined, it is from not expected error.
 *
 * If the status code is in this range, additional logging should be done before throwing error.
 *
 * @param statusCode - the status code to check
 * @returns true if the status code is not in the 2xx range
 */
export const error2XXhelper = setupError2XXhelper();
