import * as os from 'os';
import { makeValidator } from 'envalid';
import { integer } from './convertToNumeric';

export const between0AndNumCPUs = makeValidator((value: string): number => {
  const numericValue = integer(value);
  if (numericValue < 0) {
    throw new Error('number cannot be less than 0.');
  }
  if (numericValue > os.cpus().length - 1) {
    throw new Error(`number cannot be greater than number of CPUs: ${os.cpus().length}.`);
  }
  return numericValue;
});
