import { RequiredValidatorSpec, Spec, makeValidator } from 'envalid';
import { integer } from './convertToNumeric';

export const unitConverter = (
  multiplier: number,
  specs?: Spec<number>
): RequiredValidatorSpec<number> => {
  if (specs?.default) {
    specs.default *= multiplier;
  }
  if (specs?.devDefault) {
    specs.devDefault *= multiplier;
  }

  const validator = makeValidator((value: string): number => {
    const intValue = integer(value);

    return intValue * multiplier;
  });

  return validator(specs);
};

const partialHelper = (multiplier: number) => {
  return (specs: Spec<number>) => {
    return unitConverter(multiplier, specs);
  };
};

export const seconds = partialHelper(1000);
export const minutes = partialHelper(1000 * 60);
export const days = partialHelper(1000 * 60 * 60 * 24);
export const megaBytes = partialHelper(1024 * 1024);
