export type AdConfigOverride =
  | { width: number; height: number }
  | { width: string; height: string };

export interface IAdConfig {
  adsLayoutsAdditionalData: IAdsLayoutsAdditionalData;
  auditData: IAdConfigAuditData;
  config_name: string;
  src: string;
  pageType: string[];
  serviceId: string[];
  pageId: string[];
  section: { id: string; name: string }[];
  config: IPlaceholdersConfig;
}

export interface IAdsLayoutsAdditionalData {
  releaseUrl: URL;
  releaseName: string;
  releaseServices: string[];
}

export interface IAdConfigAuditData {
  releaseVersion: string;
  modifiedDate: string;
  generatedBy: string;
}

export interface IAdConfigAdSlot {
  adServer: AdConfigAdServerEnum;
  type?: string;
  slaveId?: string;
  placementId?: string;
  adUnitPath?: string;
  sizes?: [number, number][];
  kValues?: IkValues;
}

export interface IAdConfigActivationThresholds {
  offset: string | null;
  percent: number | null;
  delay: number;
}

export interface IPlaceholdersBaseDetails {
  id: string;
  enabled: boolean;
  deviceType: AdConfigDeviceTypeEnum[];
  width: string | null;
  height: string | null;
  adServer: AdConfigAdServerEnum;
  adSlots: IAdConfigAdSlot[];
  code: string;
  bidders: object[];
  AD_Config_group: string;
  AD_Config_element_id: string;
  mediaTypes: object;
  activationThresholds: IAdConfigActivationThresholds;
}

export interface IPlaceholdersDetails extends IPlaceholdersBaseDetails {
  passback?: IPlaceholdersDetails;
}

export interface IPlaceholdersConfig {
  masterId: string;
  bgPlugSrc: string | null;
  activationThresholds: IAdConfigActivationThresholds;
  trafficCategory: string[];
  placeholders: IPlaceholdersDetails[];
}

export interface IkValues {
  root: string;
  placeholder: string;
  slot?: string;
  adx?: string;
  baner_detal?: string;
}

export enum AdConfigAdServerEnum {
  ADOCEAN = 'adocean',
  GAM = 'gam'
}

export enum AdConfigDeviceTypeEnum {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  SMARTPHONE = 'smartphone'
}
