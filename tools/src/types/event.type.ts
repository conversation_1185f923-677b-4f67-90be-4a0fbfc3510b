import { Event as JREEvent } from 'json-rules-engine';
import { AdConfigOverride } from '../types';

export enum PlaceholderEnum {
  PLACEHOLDER = 'placeholder',
  COLUMN_PLACEHOLDER = 'column-placeholder'
}

export type elementsPositionsType = [number, PlaceholderPositionEnum];

export type lastPlaceholderPosition = 'last';

export interface IExcludeFacts {
  omittedFactNames: string[];
  skipOver?: boolean; // TODO: remove this line in AdsLayouts-Release-17
  skipOverFactNames?: string[]; // TODO: remove "?" in AdsLayouts-Release-17
}

export interface IPlaceholder {
  position?: PlaceholderPositionEnum;
  element?: string[]; // fact names
  placeholderPositions?: Array<number | lastPlaceholderPosition>;
  placeholderType?: PlaceholderEnum;
  countBackwards?: boolean;
  elementsIndexes?: number[];
  elementsPositions?: elementsPositionsType[];
  elementIndex?: number[];
  every?: number;
  max?: number;
  ommitLast?: boolean;
  startIndex?: number;
  exclude?: IExcludeFacts;
  enableVariant?: boolean;
}
export interface IParams {
  fact: string[];
  factPosition?: number;
  parentFact?: string;
  searchInFacts: string[];
  excludeFromSearch: string[];
  containsSomeType?: string;
  adConfigGroup: string;
  adConfigOverride?: AdConfigOverride;
  priority?: number;
  priorityGroup?: string;
  placeholder: IPlaceholder;
}

export interface IEvent extends JREEvent {
  name: string;
  desc: string;
  type: EventTypeEnum;
  params: IParams;
  rulesPackage?: string;
}

export enum EventTypeEnum {
  EVERY_X = 'everyX',
  EVERY_POSITION = 'everyPosition',
  NEAR_TO = 'nearTo',
  NEAR_TO_INDEXES = 'nearToIndexes'
}

export enum PlaceholderPositionEnum {
  ABOVE = 'above',
  UNDER = 'under'
}
