import { HttpStatus } from '@nestjs/common';
import { ResponseType } from '../types';
import { CustomHttpStatus } from './customHttpStatus';

export interface SimpleResponseInterface {
  statusCode: HttpStatus | CustomHttpStatus;
  message: string;
  reqId?: string;
}

export interface ErrorInterface extends SimpleResponseInterface {
  dateTime?: string;
  traceId?: string;
}

export interface ResponseInterface extends SimpleResponseInterface {
  type?: ResponseType;
  result?: any;
}

export interface SearchQueryName {
  name?: string;
  rulesPackage?: string;
  serviceId?: string;
  onlyNames?: boolean;
}
