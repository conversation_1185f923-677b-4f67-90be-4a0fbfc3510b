import { AccessModelEnum, AdConfigDeviceTypeEnum, PaywallEnum } from '../types';

export interface ContentMeta {
  accessModel?: AccessModelEnum;
  deviceType: AdConfigDeviceTypeEnum;
  locationInfoPageId: string | null;
  locationInfoPageType: string;
  locationInfoSectionId: string;
  locationInfoSectionName: string;
  paywall?: PaywallEnum;
  rulesPackage?: string;
  serviceEnv?: string;
  serviceId: string;
  siteVersion?: string;
  siteVersionIdentifier: string;
  time: string;
}

export type MetaElement =
  | { length?: number }
  | { height?: number }
  | { count?: number }
  | { element?: string }
  | { columns?: number; position?: number; title?: string }
  | { column?: number }
  | { variant?: string };

export interface BodyElement {
  type: string;
  id?: string;
  meta?: MetaElement;
  elements?: BodyElement[];
  direction?: string;
}

export interface CommonRequest {
  type: PageTypeEnum;
  meta: ContentMeta;
  elements: BodyElement[];
  direction: string;
}

export enum PageTypeEnum {
  article = 'article'
}
