import { Prettify } from './common.type';

interface ExtensionScheduleEntry {
  start: string;
  end: string;
  enabled: boolean;
}

export interface IExtensionSchedule {
  [key: string]: ExtensionScheduleEntry;
}

export interface IExtensionEnable {
  [key: string]: boolean;
}

export interface IExtensionAudit {
  modifiedDate: string;
  generatedBy: string;
}

export enum ExtEnableDataEnum {
  SUPER_PANEL_ENABLED = 'superPanelEnabled',
  TOP_PREMIUM_ENABLED = 'topPremiumEnabled'
}

export enum ExtScheduleDataEnum {
  SUPER_PANEL_SCHEDULE = 'superPanelSchedule',
  TOP_PREMIUM_SCHEDULE = 'topPremiumSchedule'
}

export type ExtensionConfigType = Prettify<
  { timestamp: number; audit: IExtensionAudit } & Partial<
    Record<ExtEnableDataEnum, IExtensionEnable>
  > &
    Partial<Record<ExtScheduleDataEnum, IExtensionSchedule>>
>;
