import { ServiceName } from './common.type';

export enum LogLevel {
  error = 'error',
  warn = 'warn',
  info = 'info',
  external = 'external',
  cache = 'cache',
  dev = 'dev'
}

export enum LogColors {
  black = 'black',
  red = 'red',
  green = 'green',
  yellow = 'yellow',
  blue = 'blue',
  magenta = 'magenta',
  cyan = 'cyan',
  white = 'white'
}

export type ClusterID = 'PRIMARY' | string;
export type LogDataType = object;
type FeatureEnabled = 'ENABLED' | 'DISABLED';
export type RepoEnv = {
  SERVICE_NAME: ServiceName;
  APP_ENV: ServiceEnvEnum;
  SEND_DEV_LOGS_TO_DATADOG: FeatureEnabled;
  SEND_CACHE_LOGS_TO_DATADOG: FeatureEnabled;
};
export type LoggerType = (message: string, logData?: LogDataType, logLevel?: LogLevel) => void;

export type LoggerData = {
  message: string;
  logLevel: LogLevel;
  logData?: LogDataType;
};
export type ErrorCheckResult = NoErrorResult | ErrorResult;

type NoErrorResult = {
  hasErrInstance: false;
  error: undefined;
};

type ErrorResult = {
  hasErrInstance: true;
  error: Error;
};

export enum ServiceEnvEnum {
  LOCAL = 'local',
  TEST = 'test',
  STAGE = 'stage',
  PROD = 'production'
}
