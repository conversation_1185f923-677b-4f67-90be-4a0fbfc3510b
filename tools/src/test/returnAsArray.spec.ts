import { returnAsArray, returnAsArrayEmpty } from '../utils';

describe('returnAsArray test suite', () => {
  it('should convert element to array', () => {
    expect(returnAsArray('a')).toEqual(['a']);
    expect(returnAsArray(1)).toEqual([1]);
    expect(returnAsArray({})).toEqual([{}]);
    expect(returnAsArray(null)).toEqual([null]);
    expect(returnAsArray(undefined)).toEqual([undefined]);
  });

  it('should return array', () => {
    expect(returnAsArray([])).toEqual([]);
    expect(returnAsArray([1, 2, 3])).toEqual([1, 2, 3]);
  });
});

describe('returnAsArrayEmpty test suite', () => {
  it('should convert element to array', () => {
    expect(returnAsArray('a')).toEqual(['a']);
    expect(returnAsArray(1)).toEqual([1]);
    expect(returnAsArray({})).toEqual([{}]);
  });

  it('should return array', () => {
    expect(returnAsArray([1, 2, 3])).toEqual([1, 2, 3]);
    expect(returnAsArray([])).toEqual([]);
    expect(returnAsArray([[]])).toEqual([[]]);
  });

  it('should convert null and undefined to empty array', () => {
    expect(returnAsArrayEmpty(null)).toEqual([]);
    expect(returnAsArrayEmpty(undefined)).toEqual([]);
  });
});
