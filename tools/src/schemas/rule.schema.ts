import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { Conditions, Event } from '../schemas';
import { AccessModelEnum, type IConditions, IRule, PaywallEnum } from '../types';
import { is } from '../utils';

export type RuleDocument = HydratedDocument<Rule>;

/**
 * Creates a validator function that ensures no string in an array contains a comma.
 *
 * @param fieldName - The name of the field being validated (used in the error message)
 * @returns A validator object with validator function and error message
 */
const createNoCommaValidator = (fieldName: string) => ({
  validator: (v: Array<string>) => v.every(value => !value.includes(',')),
  message: `${fieldName} should not contain comma`
});

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  },
  autoIndex: true
})
export class Rule implements IRule {
  @Prop({
    type: String,
    required: true,
    trim: true,
    unique: true,
    validate: {
      validator: (v: unknown) => is.string(v) && /^[A-Za-z0-9]*$/.test(v),
      message: (props: any) =>
        `${props.value} is not a valid event name! Please use letters and digits only.`
    }
  })
  name!: string;

  @Prop({
    type: [String],
    required: true,
    trim: true,
    lowercase: true
  })
  pageType!: string[];

  @Prop({
    type: [String],
    required: false,
    trim: true,
    validate: createNoCommaValidator('locationInfoPageType')
  })
  locationInfoPageType?: string[];

  @Prop({
    type: [String],
    required: true,
    trim: true,
    lowercase: true
  })
  serviceId!: string[];

  @Prop({
    type: [String],
    required: true,
    trim: true,
    lowercase: true
  })
  layout!: string[];

  @Prop({
    type: Boolean,
    required: true
  })
  enabled!: boolean;

  @Prop({
    type: [String],
    required: false,
    validate: createNoCommaValidator('sectionId')
  })
  sectionId?: string[];

  @Prop({
    type: [String],
    required: false,
    validate: createNoCommaValidator('pageId')
  })
  pageId?: string[];

  @Prop({
    type: String,
    required: true
  })
  conditionsName!: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Conditions.name })
  conditions!: IConditions;

  @Prop({
    type: String,
    required: true
  })
  eventName!: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: Event.name })
  event!: Event;

  @Prop({
    type: Number,
    required: false,
    default: 1
  })
  priority!: number;

  @Prop({
    type: String,
    required: false,
    trim: true
  })
  rulesPackage?: string;

  @Prop({
    type: [String],
    required: false,
    validate: createNoCommaValidator('siteVersion')
  })
  siteVersion?: string[];

  @Prop({
    type: [String],
    required: false,
    validate: createNoCommaValidator('paywall')
  })
  paywall?: PaywallEnum[];

  @Prop({
    type: [String],
    required: false,
    validate: createNoCommaValidator('accessModel')
  })
  accessModel?: AccessModelEnum[];
}

export const RuleSchema = SchemaFactory.createForClass(Rule)
  .index({ pageType: 'text', serviceId: 'text' }, { name: 'ruleModelFindIndex' })
  .index({ name: 1, rulesPackage: 1 }, { unique: true });
