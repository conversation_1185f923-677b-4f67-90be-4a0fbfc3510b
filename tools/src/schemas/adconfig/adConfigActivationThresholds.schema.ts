import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IAdConfigActivationThresholds } from '../../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdConfigActivationThresholds implements IAdConfigActivationThresholds {
  @Prop({ type: String, default: null })
  offset!: string | null;

  @Prop({ type: Number, default: null })
  percent!: number | null;

  @Prop({ type: Number })
  delay!: number;
}

export const AdConfigActivationThresholdsSchema = SchemaFactory.createForClass(
  AdConfigActivationThresholds
);
