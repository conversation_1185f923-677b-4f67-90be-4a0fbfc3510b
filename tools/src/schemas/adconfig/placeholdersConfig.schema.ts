import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  AdConfigActivationThresholds,
  AdConfigActivationThresholdsSchema
} from './adConfigActivationThresholds.schema';
import { PlaceholdersDetails, PlaceholdersDetailsSchema } from './placeholdersDetails.schema';
import { IPlaceholdersConfig } from '../../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class PlaceholdersConfig implements IPlaceholdersConfig {
  @Prop({ type: String })
  masterId!: string;

  @Prop({ type: String, default: null })
  bgPlugSrc!: string | null;

  @Prop({
    type: AdConfigActivationThresholdsSchema,
    schema: AdConfigActivationThresholdsSchema
  })
  activationThresholds!: AdConfigActivationThresholds;

  @Prop({ type: [String] })
  trafficCategory!: string[];

  @Prop({
    type: [PlaceholdersDetailsSchema],
    schema: [PlaceholdersDetailsSchema]
  })
  placeholders!: PlaceholdersDetails[];
}

export const PlaceholdersConfigSchema = SchemaFactory.createForClass(PlaceholdersConfig);
