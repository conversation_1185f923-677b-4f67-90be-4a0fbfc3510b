import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { PlaceholdersConfig, PlaceholdersConfigSchema } from './placeholdersConfig.schema';
import {
  AdsLayoutsAdditionalData,
  AdsLayoutsAdditionalDataSchema
} from './adsLayoutsAdditionalData.schema';
import { AdConfigAuditData, AdConfigAuditDataSchema } from './adConfig.auditData.schema';
import { IAdConfig } from '../../types';

export type AdConfigDocument = HydratedDocument<AdConfig>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdConfig implements IAdConfig {
  @Prop({
    type: AdsLayoutsAdditionalDataSchema,
    schema: AdsLayoutsAdditionalDataSchema
  })
  adsLayoutsAdditionalData!: AdsLayoutsAdditionalData;

  @Prop({
    type: AdConfigAuditDataSchema,
    schema: AdConfigAuditDataSchema
  })
  auditData!: AdConfigAuditData;

  @Prop({ type: String })
  config_name!: string;

  @Prop({ type: String })
  src!: string;

  @Prop({ type: [String] })
  pageType!: string[];

  @Prop({ type: [String] })
  serviceId!: string[];

  @Prop({ type: [String] })
  pageId!: string[];

  @Prop({ type: mongoose.Schema.Types.Mixed })
  section!: { id: string; name: string }[];

  @Prop({
    type: PlaceholdersConfigSchema,
    schema: PlaceholdersConfigSchema
  })
  config!: PlaceholdersConfig;
}

export const AdConfigSchema = SchemaFactory.createForClass(AdConfig).index(
  {
    pageType: 'text',
    'adsLayoutsAdditionalData.releaseServices': 'text',
    'adsLayoutsAdditionalData.releaseName': 'text'
  },
  { name: 'AdConfigModelFindIndex' }
);
