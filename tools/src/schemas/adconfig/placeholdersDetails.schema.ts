import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { AdConfigAdSlot, AdConfigAdSlotSchema } from './adConfigAdSlot.schema';
import {
  AdConfigActivationThresholds,
  AdConfigActivationThresholdsSchema
} from './adConfigActivationThresholds.schema';
import {
  AdConfigAdServerEnum,
  AdConfigDeviceTypeEnum,
  IPlaceholdersBaseDetails,
  IPlaceholdersDetails
} from '../../types';
import { returnAsArray } from '../../utils/returnAsArray';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      return ret;
    }
  }
})
export class PlaceholderBaseDetails implements IPlaceholdersBaseDetails {
  @Prop({ type: String })
  id!: string;

  @Prop({ type: Boolean })
  enabled!: boolean;

  @Prop({
    type: [String],
    required: true,
    enum: AdConfigDeviceTypeEnum,
    validate: {
      validator: (devices?: string[] | string) =>
        !!devices?.length &&
        returnAsArray(devices).every(device =>
          Object.values(AdConfigDeviceTypeEnum).includes(device as AdConfigDeviceTypeEnum)
        ),

      message: () =>
        `is required. Not provided or empty array provided. Must be equal to (one of): ${Object.values(
          AdConfigDeviceTypeEnum
        )}.`
    }
  })
  deviceType!: AdConfigDeviceTypeEnum[];

  @Prop({ type: String, default: null })
  width!: string | null;

  @Prop({ type: String, default: null })
  height!: string | null;

  @Prop({
    type: String,
    enum: AdConfigAdServerEnum
  })
  adServer!: AdConfigAdServerEnum;

  @Prop({
    type: [AdConfigAdSlotSchema],
    validate: {
      validator: (v: unknown) => Array.isArray(v) && v.length > 0,
      message: () => 'is required. Not provided or empty array provided.'
    }
  })
  adSlots!: AdConfigAdSlot[];

  @Prop({ type: String })
  code!: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  bidders!: object[];

  @Prop({ type: String, required: false, default: '' })
  AD_Config_group!: string;

  @Prop({ type: String, required: false, default: '' })
  AD_Config_element_id!: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  mediaTypes!: object;

  @Prop({
    type: AdConfigActivationThresholdsSchema,
    schema: AdConfigActivationThresholdsSchema
  })
  activationThresholds!: AdConfigActivationThresholds;
}

const PlaceholderBaseDetailsSchema = SchemaFactory.createForClass(PlaceholderBaseDetails);

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      return ret;
    }
  }
})
export class PlaceholdersDetails
  extends PlaceholderBaseDetails
  implements IPlaceholdersDetails
{
  @Prop({ type: PlaceholderBaseDetailsSchema })
  passback?: PlaceholdersDetails;
}

export const PlaceholdersDetailsSchema = SchemaFactory.createForClass(PlaceholdersDetails);
