import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { AdConfigAdServerEnum, type IAdConfigAdSlot, type IkValues } from '../../types';
import mongoose from 'mongoose';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdConfigAdSlot implements IAdConfigAdSlot {
  @Prop({
    required: true,
    enum: AdConfigAdServerEnum,
    type: String
  })
  adServer!: AdConfigAdServerEnum;

  @Prop({ type: String })
  type?: string;

  @Prop({ type: String })
  slaveId?: string;

  @Prop({ type: String })
  placementId?: string;

  @Prop({ type: String })
  adUnitPath?: string;

  @Prop({ type: mongoose.Schema.Types.Array, default: undefined })
  sizes?: [number, number][];

  @Prop({ type: mongoose.Schema.Types.Mixed })
  kValues?: IkValues;
}

export const AdConfigAdSlotSchema = SchemaFactory.createForClass(AdConfigAdSlot);
