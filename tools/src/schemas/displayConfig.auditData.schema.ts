import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { DisplayConfigAuditDataType } from '../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class DisplayConfigAuditData implements DisplayConfigAuditDataType {
  @Prop({ type: String })
  configVersion!: string;

  @Prop({ type: String })
  modifiedDate!: string;

  @Prop({ type: String })
  generatedBy!: string;
}

export const DisplayConfigAuditDataSchema =
  SchemaFactory.createForClass(DisplayConfigAuditData);
