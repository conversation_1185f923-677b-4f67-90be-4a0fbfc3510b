import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import {
  ConditionRuleEnum,
  ConditionsKindNamesEnum,
  IAllConditions,
  IAnyConditions,
  IConditions,
  IFlatConditionProperties,
  type IFlatConditionValue,
  OperatorEnum
} from '../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class FlatConditionValue implements IFlatConditionValue {
  @Prop({ type: String })
  type?: string;

  @Prop({ type: Number })
  count?: number;

  @Prop({ type: String })
  expectedFact?: string;

  @Prop({ type: Number })
  position?: number;

  @Prop({ type: [String], default: undefined })
  excludedFacts?: string[];

  @Prop({ type: Boolean })
  expectOneFactOnly?: boolean;

  @Prop({ type: String, enum: ConditionRuleEnum })
  rule?: ConditionRuleEnum;
}

export const FlatConditionValueSchema = SchemaFactory.createForClass(FlatConditionValue);

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class ConditionProperties implements IFlatConditionProperties {
  @Prop({ type: String, required: true })
  fact!: string;

  @Prop({ type: String, enum: OperatorEnum, required: true })
  operator!: OperatorEnum;

  @Prop({ type: FlatConditionValueSchema, required: true })
  value!: FlatConditionValue;
}

export const ConditionPropertiesSchema = SchemaFactory.createForClass(ConditionProperties);

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      delete ret.kind;
      return ret;
    }
  }
})
export class AllConditions implements IAllConditions {
  name!: string;
  rulesPackage?: string;
  kind = ConditionsKindNamesEnum.All as const;

  @Prop({ type: [ConditionPropertiesSchema], required: true })
  all!: ConditionProperties[];
}

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      delete ret.kind;
      return ret;
    }
  }
})
export class AnyConditions implements IAnyConditions {
  name!: string;
  rulesPackage?: string;
  kind = ConditionsKindNamesEnum.Any as const;

  @Prop({ type: [ConditionPropertiesSchema], required: true })
  any!: ConditionProperties[];
}

@Schema({
  discriminatorKey: 'kind',
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      //   delete ret._id;
      delete ret.id;
      delete ret.kind;
      return ret;
    }
  }
})
export class Conditions implements IConditions {
  @Prop({
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: (v: unknown) => typeof v === 'string' && /^[A-Za-z0-9]*$/.test(v),
      message: (props: any) =>
        `${props.value} is not a valid condition name! Please use letters and digits only.`
    }
  })
  name!: string;

  @Prop({ type: String })
  rulesPackage?: string;

  @Prop({
    type: String,
    required: true,
    enum: ConditionsKindNamesEnum
  })
  kind!: ConditionsKindNamesEnum;
}

export const AllConditionsSchema = SchemaFactory.createForClass(AllConditions);
export type AllConditionsDocument = HydratedDocument<AllConditions>;

export const AnyConditionsSchema = SchemaFactory.createForClass(AnyConditions);
export type AnyConditionsDocument = HydratedDocument<AnyConditions>;

export const ConditionsSchema = SchemaFactory.createForClass(Conditions);
ConditionsSchema.index({ name: 1, rulesPackage: 1 }, { unique: true });

export type ConditionsDocument = HydratedDocument<AllConditions | AnyConditions>;
