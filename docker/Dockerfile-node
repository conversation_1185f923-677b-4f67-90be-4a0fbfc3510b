# ====== Node build ========

ARG BASE_IMAGE=node:22

FROM $BASE_IMAGE as ubuntu

ARG DEBIAN_FRONTEND=noninteractive

ENV TZ=Europe/Warsaw
ENV PORT=8080

WORKDIR /home/<USER>/

RUN apt-get update && DEBIAN_FRONTEND=noninteractive && apt-get install -y \
  tzdata \
  curl \
  git \
  zsh \
  build-essential \
  jq \
  && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
  && dpkg-reconfigure --frontend noninteractive tzdata \
  && apt-get clean \
  && mkdir -p /home/<USER>/ \
  && useradd -d /home/<USER>/ sdkuser \
  && chown sdkuser:sdkuser /home/<USER>/

COPY --chown=sdkuser:sdkuser . /home/<USER>/

RUN cd /home/<USER>
  && npm install pm2 n -g \
  && su - sdkuser -c 'npm install' \
  && su - sdkuser -c 'npm run build'

RUN mkdir -p /home/<USER>/logs/ \
  && chown -R sdkuser:sdkuser /home/<USER>/logs/ \
  && mkdir -p /home/<USER>/.npm/_cacache/tmp/ \
  && chown -R sdkuser:sdkuser /home/<USER>/.npm/_cacache/tmp/ \
  && ln -s /home/<USER>/.npm/_cacache/tmp/ /home/<USER>/logs/cacache_tmp

USER sdkuser
VOLUME ["/home/<USER>/logs"]

ADD docker/config/entrypoint_ci.sh /usr/local/bin/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]

HEALTHCHECK --interval=60s --timeout=5s --start-period=20s \
  CMD curl -f http://localhost:4002/check 2>&1 | grep -P '^OK' || exit 1
