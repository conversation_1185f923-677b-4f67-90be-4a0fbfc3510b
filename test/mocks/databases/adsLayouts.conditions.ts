import { ConditionsKindNamesEnum, OperatorEnum } from 'ads-layouts-tools';
import { Types } from 'mongoose';

const { ObjectId } = Types;

export const conditionsDB = [
  {
    _id: new ObjectId('666177f1843edf9d16738248'),
    name: 'HCcondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb6638848cc2a610978d1'),
    name: 'adexDetal',
    kind: ConditionsKindNamesEnum.Any,
    any: [
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { count: 1, type: 'tags' }
      },
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { count: 1, type: 'share' }
      },
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { count: 1, type: 'taboola' }
      }
    ]
  },
  {
    _id: new ObjectId('651eb8158848cc2a61097933'),
    name: 'banerDetal1',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'lead', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb5ae8848cc2a61097885'),
    name: 'banerDetalX',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'storyline',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'paragraph', count: 4 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb5c88848cc2a61097895'),
    name: 'brandingPlayer',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'header',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'main-multimedium', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('66f43016991b72ecf11f6015'),
    name: 'commercialConditionDD',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'footer', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('66e1bff30b4188099ca66347'),
    name: 'commercialConditionDzienDobry',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'footer', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb6428848cc2a610978c1'),
    name: 'lewyMargines',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-narrow',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'related', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb6aa8848cc2a610978f1'),
    name: 'lewyMarginesInherited',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-narrow',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'narrow-inherited', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb5238848cc2a61097871'),
    name: 'onTop2',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb5f58848cc2a610978a5'),
    name: 'onTopZeScreeningiem',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb62a8848cc2a610978b5'),
    name: 'panelPodXArtykulem',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'news-list', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eb6898848cc2a610978e1'),
    name: 'prawaSzpaltaRelated',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-narrow',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'related', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('651eba3c8848cc2a61097997'),
    name: 'underMain',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'main', count: 1 }
      }
    ]
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe4a'),
    name: 'banerDetal1',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'column-wide',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'lead', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdfd'),
    name: 'halfpage2Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdfa'),
    name: 'halfpage1ConditionWariant2to5',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 10,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe26'),
    name: 'panel9ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdd3'),
    name: 'panel1Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'screening-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe2c'),
    name: 'native2ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 2 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdd0'),
    name: 'commercialCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'footer', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe53'),
    name: 'hasTeaserGridModule',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'teaser-grid-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe0c'),
    name: 'panel3ConditionMobileWariant1',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 8,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe3b'),
    name: 'leadInMainCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'lead', count: 1, excludedFacts: [] }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe03'),
    name: 'halfpage4Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 2 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe3e'),
    name: 'banerDetalX',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'storyline',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'paragraph', count: 4 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe32'),
    name: 'hasTGMinPagination',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'pagination',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'teaser-grid-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe47'),
    name: 'conditionCommercialFooter',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'footer', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe44'),
    name: 'conditionArticleLayer',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdf7'),
    name: 'halfpage1Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 9,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdee'),
    name: 'native2ConditionWariant2to5',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 10,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdf4'),
    name: 'native4Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 2 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe09'),
    name: 'panel2ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe2f'),
    name: 'hasPaginationSSCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'pagination', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdca'),
    name: 'layerCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdd9'),
    name: 'panel3Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe17'),
    name: 'panel4ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe1d'),
    name: 'panel6ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afde5'),
    name: 'native1Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 9,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe23'),
    name: 'panel8ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afddc'),
    name: 'panel4Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afde8'),
    name: 'native1ConditionWariant2to5',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 10,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afde2'),
    name: 'panel6Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe20'),
    name: 'panel7ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe41'),
    name: 'halfpageColumnNarrowCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'columns',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'column-narrow', count: 1, excludedFacts: [] }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe50'),
    name: 'hasGlobalNews',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe29'),
    name: 'native1ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe1a'),
    name: 'panel5ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe00'),
    name: 'halfpage3Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe0f'),
    name: 'panel3ConditionMobileWariant2to4',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 9,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe38'),
    name: 'recommendationQueuePanelCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'recommendation-queue', count: 1, excludedFacts: [] }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe35'),
    name: 'hasTeaserGridModuleInRecommendationsQueue',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'recommendation-queue',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'teaser-grid-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdf1'),
    name: 'native3Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'information-modules',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'category-module', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe06'),
    name: 'panel1ConditionMobile',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdcd'),
    name: 'topPremiumCondition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'elements',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'top-menu', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe4d'),
    name: 'hasInformationModules',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdd6'),
    name: 'panel2Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'global-news', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afddf'),
    name: 'panel5Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'main',
        operator: OperatorEnum.CONTAINS_OF_TYPE,
        value: { type: 'information-modules', count: 1 }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdeb'),
    name: 'native2Condition',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 9,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afe12'),
    name: 'panel3ConditionMobileWariant5',
    kind: ConditionsKindNamesEnum.All,
    all: [
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 7,
          excludedFacts: ['placeholder']
        }
      },
      {
        fact: 'global-news',
        operator: OperatorEnum.EXPECTED_POSITION,
        value: {
          expectedFact: 'multi-teaser-module',
          position: 9,
          excludedFacts: ['placeholder']
        }
      }
    ],
    rulesPackage: 'test_package'
  }
];
