import { DisplayConfig } from 'ads-layouts-tools';
import { commonMockDisplayConfigData } from '../DisplayConfig';

export const displayConfigDB: DisplayConfig[] = [
  {
    service: 'TEST',
    release: 'release/1.0.0/',
    ...commonMockDisplayConfigData
  },
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    release: 'release/1.63.0/'
  },
  {
    ...commonMockDisplayConfigData,
    service: '19',
    release: 'release/1.64.0/'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    release: 'release/1.60.0/'
  },
  {
    ...commonMockDisplayConfigData,
    env: 'non_existent_env',
    service: 'biznes',
    release: 'release/1.60.0/'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'warszawa_k2',
    siteVersion: 'ab_atsdk_gb',
    release: 'release/1.64.4/'
  }
];
