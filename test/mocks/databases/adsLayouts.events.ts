import { Types } from 'mongoose';

const { ObjectId } = Types;

type nearToEvent = { type: 'nearTo' } & Record<string, any>;
type nearToIndexesEvent = { type: 'nearToIndexes' } & Record<string, any>;
type everyPositionEvent = { type: 'everyPosition' } & Record<string, any>;
type everyXEvent = { type: 'everyX' } & Record<string, any>;

const nearToEvents: nearToEvent[] = [
  {
    _id: new ObjectId('651eba408848cc2a6109799b'),
    desc: 'under main',
    name: 'underMain',
    params: {
      adConfigGroup: 'layer',
      adConfigOverride: { height: '0px', width: '0px' },
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['main'],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('66617889843edf9d16738266'),
    desc: 'aaa',
    name: 'HCevent',
    params: {
      adConfigGroup: 'panel_pod_artykulem',
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['top-menu'],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('651eb5ef8848cc2a610978a1'),
    desc: 'under top-menu',
    name: 'onTopZeScreeningiem',
    params: {
      adConfigGroup: 'on_top',
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['top-menu'],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('66e1bfe70b4188099ca66343'),
    desc: 'desc',
    name: 'commercialEventDzienDobry',
    params: {
      adConfigGroup: 'commercial_break',
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['footer'],
        position: 'above'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('66f43076991b72ecf11f6019'),
    desc: 'desc',
    name: 'commercialEventDD',
    params: {
      adConfigGroup: 'commercial_break',
      adConfigOverride: { height: '0px', width: '0px' },
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['footer'],
        position: 'above'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('651eb5208848cc2a6109786d'),
    desc: 'above menu',
    name: 'onTop2',
    params: {
      adConfigGroup: 'layer',
      adConfigOverride: { height: '0px', width: '0px' },
      excludeFromSearch: [],
      fact: ['elements'],
      placeholder: {
        element: ['top-menu'],
        position: 'above'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('651eb80d8848cc2a6109792f'),
    desc: 'baner_detal_1',
    name: 'banerDetal1',
    params: {
      adConfigGroup: 'baner_detal',
      excludeFromSearch: [],
      fact: ['column-wide'],
      placeholder: {
        element: ['lead'],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearTo'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd93'),
    name: 'eventCommercialOverride',
    desc: 'desc',
    type: 'nearTo',
    params: {
      fact: ['elements'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'commercial_break',
      adConfigOverride: {
        width: '0px',
        height: '0px'
      },
      placeholder: {
        position: 'above',
        element: ['footer']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd90'),
    name: 'eventArticleLayer',
    desc: 'desc',
    type: 'nearTo',
    params: {
      fact: ['elements'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'layer',
      adConfigOverride: {
        width: '0px',
        height: '0px'
      },
      placeholder: {
        position: 'under',
        element: ['footer']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcb8'),
    name: 'eventLayer',
    desc: 'desc',
    type: 'nearTo',
    params: {
      fact: ['elements'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'layer',
      placeholder: {
        position: 'under',
        element: ['footer']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcbe'),
    name: 'commercialEvent',
    desc: 'desc',
    type: 'nearTo',
    params: {
      fact: ['elements'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'commercial_break',
      placeholder: {
        position: 'above',
        element: ['footer']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd96'),
    name: 'panelInArticle1Event',
    desc: 'Event for article 1 in panel',
    type: 'nearTo',
    params: {
      fact: ['column-wide'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: ['lead']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd69'),
    name: 'panelInArticle1MobileEvent',
    desc: 'Event for mobile article 1 in panel',
    type: 'nearTo',
    params: {
      fact: ['main'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: ['lead']
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd66'),
    name: 'recommendationQueuePanelEvent',
    desc: 'event for placeholder above recommendation-queue in panel',
    type: 'nearTo',
    params: {
      fact: ['main'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['recommendation-queue']
      }
    },
    rulesPackage: 'test_package'
  }
];

const nearToIndexesEvents: nearToIndexesEvent[] = [
  {
    _id: new ObjectId('65251a568848cc2a61098346'),
    desc: 'under tags or under share if tags unavailable',
    name: 'adexDetal',
    params: {
      adConfigGroup: 'adex_detal',
      excludeFromSearch: [],
      fact: ['column-wide'],
      placeholder: {
        element: ['taboola', 'tags', 'share'],
        elementsIndexes: [1],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearToIndexes'
  },
  {
    _id: new ObjectId('65251a568848cc2a61098347'),
    desc: 'under tags or under share if tags unavailable, for any fact',
    name: 'adexDetalAnyFact',
    params: {
      adConfigGroup: 'adex_detal',
      excludeFromSearch: [],
      fact: ['any'],
      parentFact: 'main',
      factPosition: 1,
      placeholder: {
        element: ['taboola', 'tags', 'share'],
        elementsIndexes: [1],
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'nearToIndexes'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcd9'),
    name: 'native2Event',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_2',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd30'),
    name: 'native1SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 1,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_1',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd3c'),
    name: 'native5SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_5',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd81'),
    name: 'panel1to10DetalK2Event5',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 5,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd21'),
    name: 'panel4SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_4',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd09'),
    name: 'panel7EventMobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 1,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_7',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afce2'),
    name: 'native4Event',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 2,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_4',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        countBackwards: true,
        elementsIndexes: [2]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd3f'),
    name: 'native6SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_6',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd42'),
    name: 'native7SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_7',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd6c'),
    name: 'native1to10DetalK2Event1',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 1,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd60'),
    name: 'native1to10EventK2Detal',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['recommendation-queue'],
      searchInFacts: ['teaser-grid-module'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 5]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afda2'),
    name: 'warszawaK2native1to6EventGN',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['global-news'],
      searchInFacts: ['teaser-grid-module'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 5]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdc3'),
    name: 'warszawaK2panel1to14RuleIMteasers6Mobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module', 'teaser-layout-module'],
      factPosition: 6,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd84'),
    name: 'panel1to10DetalK2Event2',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 2,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd1e'),
    name: 'panel3SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_3',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdc6'),
    name: 'warszawaK2native1to6EventIMmobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['information-modules'],
      searchInFacts: ['category-module', 'teaser-layout-module'],
      excludeFromSearch: [],
      containsSomeType: 'teaser',
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd78'),
    name: 'native1to10DetalK2Event5',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 5,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcdf'),
    name: 'native3Event',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 1,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_3',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        countBackwards: true,
        elementsIndexes: [2]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd39'),
    name: 'native4SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 2,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_4',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd72'),
    name: 'panel1to10DetalK2Event1',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 1,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcdc'),
    name: 'native2EventWariant2to5',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_2',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd2d'),
    name: 'panel9SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_9',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [9]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd27'),
    name: 'panel7SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_7',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [7]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdbd'),
    name: 'warszawaK2panel1to14RuleIMteasers4Mobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module', 'teaser-layout-module'],
      factPosition: 4,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afda5'),
    name: 'warszawaK2native1to6EventIM',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['information-modules'],
      searchInFacts: ['category-module', 'teaser-layout-module'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        countBackwards: true,
        elementsIndexes: [2]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd6f'),
    name: 'native1to10DetalK2Event2',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 2,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd7e'),
    name: 'panel1to10DetalK2Event4',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd2a'),
    name: 'panel8SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_8',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [8]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd15'),
    name: 'native2EventMobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 2,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_2',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd36'),
    name: 'native3SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 2,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_3',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcd3'),
    name: 'native1Event',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_1',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcd6'),
    name: 'native1EventWariant2to5',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_1',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd1b'),
    name: 'panel2SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_2',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [2]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd75'),
    name: 'native1to10DetalK2Event4',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd18'),
    name: 'panel1SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_1',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [1]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdc0'),
    name: 'warszawaK2panel1to14RuleIMteasers5Mobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module', 'teaser-layout-module'],
      factPosition: 5,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd12'),
    name: 'native1EventMobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 1,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_1',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd7b'),
    name: 'panel1to10DetalK2Event3',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd5a'),
    name: 'panel6SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_6',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd99'),
    name: 'native1to10DetalK2Event3',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'recommendation-queue',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [2, 6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd24'),
    name: 'panel5SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['pagination'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_5',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [5]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdb7'),
    name: 'warszawaK2panel1to14RuleIMteasers2Mobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module', 'teaser-layout-module'],
      factPosition: 2,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdba'),
    name: 'warszawaK2panel1to14RuleIMteasers3Mobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module', 'teaser-layout-module'],
      factPosition: 3,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [4]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd33'),
    name: 'native2SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 1,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_2',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd5d'),
    name: 'panel2to6EventDetalK2',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['recommendation-queue'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        element: ['teaser-grid-module'],
        elementsIndexes: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd0f'),
    name: 'panel9EventMobile',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['category-module'],
      factPosition: 2,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_9',
      placeholder: {
        position: 'under',
        element: ['teaser'],
        elementsIndexes: [3]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd45'),
    name: 'native8SSEvent',
    desc: 'desc',
    type: 'nearToIndexes',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_8',
      placeholder: {
        position: 'above',
        element: ['teaser'],
        elementsIndexes: [6]
      }
    },
    rulesPackage: 'test_package'
  }
];

const everyPositionEvents: everyPositionEvent[] = [
  {
    _id: new ObjectId('651eb5c18848cc2a61097891'),
    desc: 'branding player',
    name: 'brandingPlayer',
    params: {
      adConfigGroup: 'branding_playera_main',
      excludeFromSearch: [],
      fact: ['main-multimedium'],
      placeholder: {
        element: [],
        placeholderPositions: [1]
      },
      searchInFacts: []
    },
    type: 'everyPosition'
  },
  {
    _id: new ObjectId('65251a2e8848cc2a61098326'),
    desc: 'under third element in related in column-narrow',
    name: 'lewyMargines',
    params: {
      adConfigGroup: 'lewy_margines',
      adConfigOverride: { height: '620px', width: '300px' },
      excludeFromSearch: [],
      fact: ['related'],
      placeholder: {
        element: [],
        elementsPositions: [[3, 'under']]
      },
      priority: 1,
      searchInFacts: []
    },
    type: 'everyPosition'
  },
  {
    _id: new ObjectId('652519f28848cc2a61098311'),
    desc: 'under first element in related in column-narrow',
    name: 'prawaSzpaltaRelated',
    params: {
      adConfigGroup: 'prawa_szpalta',
      adConfigOverride: { height: '620px', width: '300px' },
      excludeFromSearch: [],
      fact: ['related'],
      placeholder: {
        element: [],
        elementsPositions: [[1, 'under']]
      },
      priority: 1,
      searchInFacts: []
    },
    type: 'everyPosition'
  },
  {
    _id: new ObjectId('652519d08848cc2a61098300'),
    desc: 'under second element in narrow-inherited in column-narrow',
    name: 'lewyMarginesInherited',
    params: {
      adConfigGroup: 'lewy_margines',
      adConfigOverride: { height: '620px', width: '300px' },
      excludeFromSearch: [],
      fact: ['narrow-inherited'],
      placeholder: {
        element: [],
        elementsPositions: [[2, 'under']]
      },
      priority: 1,
      searchInFacts: []
    },
    type: 'everyPosition'
  },
  {
    _id: new ObjectId('651ff3308848cc2a61097ba2'),
    desc: 'panel pod X artykulem',
    name: 'panelPodXArtykulem',
    params: {
      adConfigGroup: 'panel_pod_artykulem',
      excludeFromSearch: [],
      fact: ['news-list'],
      placeholder: {
        element: [],
        elementsPositions: [
          [1, 'under'],
          [3, 'under'],
          [5, 'under'],
          [7, 'under'],
          [9, 'under'],
          [13, 'under'],
          [18, 'under'],
          [23, 'under'],
          [28, 'under']
        ]
      },
      searchInFacts: []
    },
    type: 'everyPosition'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afceb'),
    name: 'halfpage2Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['featured-teaser-module'],
      factPosition: 1,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_2',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcf1'),
    name: 'halfpage4Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['category-module'],
      factPosition: 2,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_4',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afce8'),
    name: 'halfpage1EventWariant2to5',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_1',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd4e'),
    name: 'halfpage3SSEvent',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_3',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcee'),
    name: 'halfpage3Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['category-module'],
      factPosition: 1,
      parentFact: 'information-modules',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_3',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd4b'),
    name: 'halfpage2SSEvent',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 2,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_2',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdb4'),
    name: 'panel1to15EventIM',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        elementsPositions: [
          [1, 'under'],
          [2, 'under'],
          [3, 'under'],
          [4, 'under'],
          [5, 'under']
        ]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd8d'),
    name: 'halfpageColumnNarrowEvent',
    desc: 'Last placeholder in column-narrow',
    type: 'everyPosition',
    params: {
      fact: ['column-narrow'],
      parentFact: 'columns',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcc7'),
    name: 'panel3Event',
    desc: 'po 9. module w sekcji type: global-news',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_3',
      placeholder: {
        elementsPositions: [[9, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },

  {
    _id: new ObjectId('679bf1ef85328b28940afcfd'),
    name: 'panel3EventMobileWariant2to4',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_3',
      placeholder: {
        elementsPositions: [[5, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcbb'),
    name: 'topPremiumEvent',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['top-menu'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'top_premium',
      placeholder: {
        placeholderPositions: [1]
      }
    },
    rulesPackage: 'test_package'
  },

  {
    _id: new ObjectId('679bf1ef85328b28940afdb1'),
    name: 'panel1to15EventGN',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        elementsPositions: [
          [2, 'under'],
          [3, 'under'],
          [5, 'under'],
          [7, 'under'],
          [8, 'under']
        ]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcc4'),
    name: 'panel2Event',
    desc: 'po 5. module w sekcji type: global-news',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_2',
      placeholder: {
        elementsPositions: [[7, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd48'),
    name: 'halfpage1SSEvent',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 1,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_1',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcf7'),
    name: 'panel2EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_2',
      placeholder: {
        elementsPositions: [[3, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcca'),
    name: 'panel4Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_4',
      placeholder: {
        elementsPositions: [[1, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd06'),
    name: 'panel6EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_6',
      placeholder: {
        elementsPositions: [[1, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd9c'),
    name: 'warszawaK2Halfpage1to6Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: ['all'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'halfpage',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd54'),
    name: 'native1SSEventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 3,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_1',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd00'),
    name: 'panel4EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_4',
      placeholder: {
        elementsPositions: [[7, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcd0'),
    name: 'panel6Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_6',
      placeholder: {
        elementsPositions: [[3, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afda8'),
    name: 'warszawaK2panel1to9EventGN',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        elementsPositions: [
          [3, 'under'],
          [7, 'under'],
          [9, 'under']
        ]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd03'),
    name: 'panel5EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_5',
      placeholder: {
        elementsPositions: [[8, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcc1'),
    name: 'panel1Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['screening-module'],
      factPosition: 1,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_1',
      placeholder: {
        placeholderPositions: [1]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd9f'),
    name: 'warszawaK2halfpage1to6EventIM',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: ['all'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'halfpage',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd63'),
    name: 'halfpage2to6EventK2Detal',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['recommendation-queue'],
      searchInFacts: ['teaser-grid-module'],
      excludeFromSearch: [],
      containsSomeType: 'column-placeholder',
      adConfigGroup: 'halfpage',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd0c'),
    name: 'panel8EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_8',
      placeholder: {
        elementsPositions: [[2, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afce5'),
    name: 'halfpage1Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'global-news',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_1',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd51'),
    name: 'halfpage4SSEvent',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'halfpage_4',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'column-placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd57'),
    name: 'native2SSEventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['teaser-grid-module'],
      factPosition: 4,
      parentFact: 'pagination',
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'native_2',
      placeholder: {
        placeholderPositions: ['last'],
        placeholderType: 'placeholder'
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcfa'),
    name: 'panel3EventMobileWariant1and5',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_3',
      placeholder: {
        elementsPositions: [[6, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afcf4'),
    name: 'panel1EventMobile',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['global-news'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_1',
      placeholder: {
        elementsPositions: [[2, 'under']]
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afccd'),
    name: 'panel5Event',
    desc: 'desc',
    type: 'everyPosition',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_5',
      placeholder: {
        elementsPositions: [[2, 'under']]
      }
    },
    rulesPackage: 'test_package'
  }
];

const everyXEvents: everyXEvent[] = [
  {
    _id: new ObjectId('651eb5aa8848cc2a61097881'),
    desc: 'baner detal x',
    name: 'banerDetalX',
    params: {
      adConfigGroup: 'baner_detal',
      excludeFromSearch: [],
      fact: ['storyline'],
      placeholder: {
        element: ['paragraph'],
        every: 4,
        max: 10,
        ommitLast: true,
        position: 'under'
      },
      searchInFacts: []
    },
    type: 'everyX'
  },
  {
    _id: new ObjectId('679bf32985328b28940aff27'),
    name: 'panelInArticleXMobileEventV2',
    desc: 'Panel in article every x type of event',
    type: 'everyX',
    params: {
      fact: ['storyline'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: [],
        every: 3,
        max: 10,
        ommitLast: true,
        exclude: {
          omittedFactNames: [
            'dashboard',
            'file',
            'html',
            'photo',
            'infographic',
            'gallery',
            'teaser',
            'map',
            'social-embed',
            'teaser-gallery',
            'video',
            'playlist',
            'xlink',
            'subhead',
            'lead'
          ],
          skipOver: false
        }
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd87'),
    name: 'panelInArticleXMobileEvent',
    desc: 'Panel in article every x type of event',
    type: 'everyX',
    params: {
      fact: ['storyline'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: ['paragraph'],
        every: 3,
        max: 10,
        ommitLast: true
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afd8a'),
    name: 'panelInArticleXEvent',
    desc: 'Panel in article every x type of event',
    type: 'everyX',
    params: {
      fact: ['storyline'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: ['paragraph'],
        every: 4,
        max: 10,
        ommitLast: true
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf32985328b28940aff24'),
    name: 'panelInArticleXEventV2',
    desc: 'Panel in article every x type of event',
    type: 'everyX',
    params: {
      fact: ['storyline'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel_in_article',
      placeholder: {
        position: 'under',
        element: [],
        every: 4,
        max: 10,
        ommitLast: true,
        exclude: {
          omittedFactNames: [
            'dashboard',
            'file',
            'html',
            'photo',
            'infographic',
            'gallery',
            'teaser',
            'map',
            'social-embed',
            'teaser-gallery',
            'video',
            'playlist',
            'xlink',
            'subhead',
            'lead'
          ],
          skipOver: true
        }
      }
    },
    rulesPackage: 'test_package'
  },
  {
    _id: new ObjectId('679bf1ef85328b28940afdab'),
    name: 'warszawaK2panel1to9Event',
    desc: 'desc',
    type: 'everyX',
    params: {
      fact: ['information-modules'],
      searchInFacts: [],
      excludeFromSearch: [],
      adConfigGroup: 'panel',
      placeholder: {
        position: 'under',
        every: 1,
        max: 100,
        ommitLast: true
      }
    },
    rulesPackage: 'test_package'
  }
];

export const eventsDB = [
  ...nearToEvents,
  ...nearToIndexesEvents,
  ...everyPositionEvents,
  ...everyXEvents
];
