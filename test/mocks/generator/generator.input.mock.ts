import { CommonRequest, PageTypeEnum } from 'InterfacesAndTypes';
import { AdConfigDeviceTypeEnum } from 'ads-layouts-tools';
import { cloneDeep } from 'lodash';

export const body_1: CommonRequest = {
  type: PageTypeEnum.article,
  meta: {
    deviceType: AdConfigDeviceTypeEnum.DESKTOP,
    locationInfoPageId: '7931689',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '222',
    locationInfoSectionName: 'Gwiazdy',
    serviceEnv: 'production',
    serviceId: 'ddtvn',
    siteVersion: 'ab_atsdk_ga',
    time: '1716539400000',
    siteVersionIdentifier: '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12'
  },
  elements: [
    { type: 'placeholder', id: '1' },
    { type: 'top-menu' },
    { type: 'placeholder', id: '2' },
    {
      type: 'main',
      elements: [
        { type: 'placeholder', id: '4' },
        {
          type: 'header',
          elements: [
            { type: 'placeholder', id: '7' },
            { type: 'title', meta: { length: 97 } },
            { type: 'placeholder', id: '9' },
            { type: 'meta', meta: { height: 54 } },
            { type: 'placeholder', id: '10' },
            {
              type: 'main-multimedium',
              meta: { height: 724 },
              elements: [{ type: 'placeholder', id: '12' }],
              direction: 'vertical'
            },
            { type: 'placeholder', id: '14' },
            { type: 'share', meta: { height: 60 } },
            { type: 'placeholder', id: '16' }
          ],
          direction: 'vertical'
        },
        { type: 'placeholder', id: '17' },
        {
          type: 'columns',
          elements: [
            {
              type: 'column-wide',
              elements: [
                { type: 'placeholder', id: '22' },
                { type: 'lead', meta: { length: 195 } },
                { type: 'placeholder', id: '23' },
                {
                  type: 'storyline',
                  elements: [
                    { type: 'placeholder', id: '26' },
                    { type: 'subhead', meta: { length: 32 } },
                    { type: 'placeholder', id: '28' },
                    { type: 'paragraph', meta: { length: 457 } },
                    { type: 'placeholder', id: '30' },
                    { type: 'paragraph', meta: { length: 183 } },
                    { type: 'placeholder', id: '31' },
                    {
                      type: 'dashboard',
                      elements: [
                        { type: 'placeholder', id: '34' },
                        { type: 'teaser', meta: { height: 120 } },
                        { type: 'placeholder', id: '36' },
                        { type: 'teaser', meta: { height: 120 } },
                        { type: 'placeholder', id: '38' }
                      ],
                      direction: 'vertical'
                    },
                    { type: 'placeholder', id: '40' },
                    { type: 'subhead', meta: { length: 60 } },
                    { type: 'placeholder', id: '42' },
                    { type: 'paragraph', meta: { length: 419 } },
                    { type: 'placeholder', id: '44' },
                    { type: 'paragraph', meta: { length: 181 } },
                    { type: 'placeholder', id: '46' },
                    { type: 'social-embed', meta: { height: 360 } },
                    { type: 'placeholder', id: '48' },
                    { type: 'social-embed', meta: { height: 360 } },
                    { type: 'placeholder', id: '50' },
                    { type: 'paragraph', meta: { length: 187 } },
                    { type: 'placeholder', id: '52' },
                    { type: 'paragraph', meta: { length: 14 } },
                    { type: 'placeholder', id: '54' },
                    { type: 'list', meta: {} },
                    { type: 'placeholder', id: '56' }
                  ],
                  direction: 'vertical'
                },
                { type: 'placeholder', id: '58' },
                { type: 'author', meta: { length: 14 } },
                { type: 'placeholder', id: '60' },
                { type: 'source', meta: { length: 10 } },
                { type: 'placeholder', id: '62' },
                { type: 'main-picture-source', meta: { length: 7 } },
                { type: 'placeholder', id: '64' },
                { type: 'share', meta: { height: 60 } },
                { type: 'placeholder', id: '66' },
                { type: 'tags', meta: { count: 5 } },
                { type: 'placeholder', id: '68' },
                { type: 'taboola' },
                { type: 'placeholder', id: '69' },
                {
                  type: 'news-list',
                  elements: [
                    { type: 'placeholder', id: '72' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '74' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '76' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '78' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '80' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '82' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '84' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '86' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '88' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '90' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '92' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '94' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '96' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '98' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '100' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '102' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '104' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '106' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '108' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '110' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '112' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '114' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '116' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '118' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '120' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '122' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '124' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '126' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '128' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '130' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '132' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '134' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '136' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '138' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '140' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '142' },
                    { type: 'teaser', meta: { height: 420 } },
                    { type: 'placeholder', id: '144' }
                  ],
                  direction: 'vertical'
                },
                { type: 'placeholder', id: '146' }
              ],
              direction: 'vertical'
            },
            {
              type: 'column-narrow',
              elements: [
                { type: 'placeholder', id: '149' },
                {
                  type: 'related',
                  elements: [
                    { type: 'placeholder', id: '152' },
                    { type: 'teaser', meta: { height: 231 } },
                    { type: 'placeholder', id: '154' },
                    { type: 'teaser', meta: { height: 231 } },
                    { type: 'placeholder', id: '156' },
                    { type: 'teaser', meta: { height: 231 } },
                    { type: 'placeholder', id: '158' }
                  ],
                  direction: 'vertical'
                },
                { type: 'placeholder', id: '160' }
              ],
              direction: 'vertical'
            }
          ],
          direction: 'horizontal'
        },
        { type: 'placeholder', id: '164' }
      ],
      direction: 'vertical'
    },
    { type: 'placeholder', id: '166' },
    { type: 'footer' },
    { type: 'placeholder', id: '168' }
  ],
  direction: 'vertical'
};

export const body_2: CommonRequest = {
  type: PageTypeEnum.article,
  meta: {
    deviceType: AdConfigDeviceTypeEnum.SMARTPHONE,
    locationInfoPageId: '5780',
    locationInfoPageType: 'story_story_video',
    locationInfoSectionId: '773',
    locationInfoSectionName: 'Śródmieście',
    serviceEnv: 'prev-release-5-22-0',
    serviceId: 'warszawa_k2',
    siteVersion: 'ab_atsdk_gb',
    time: '1729620000000',
    siteVersionIdentifier: '5780_20241015120643_773_9f3310395be0678865aa1bc5c951ed82'
  },
  elements: [
    { id: '0', type: 'placeholder' },
    { type: 'top-menu', elements: [{ id: '2', type: 'placeholder' }], direction: 'vertical' },
    { id: '3', type: 'placeholder' },
    {
      type: 'main',
      elements: [
        { id: '5', type: 'placeholder' },
        {
          type: 'header',
          elements: [
            { id: '8', type: 'placeholder' },
            { type: 'title', meta: { length: 48 } },
            { id: '10', type: 'placeholder' },
            { type: 'meta', meta: { height: 22 } },
            { id: '12', type: 'placeholder' }
          ],
          direction: 'vertical'
        },
        { id: '13', type: 'placeholder' },
        {
          type: 'main-multimedium',
          meta: { height: 724 },
          elements: [{ id: '17', type: 'placeholder' }],
          direction: 'vertical'
        },
        { id: '19', type: 'placeholder' },
        { type: 'lead', meta: { length: 213 } },
        { id: '20', type: 'placeholder' },
        {
          type: 'storyline',
          elements: [
            { id: '22', type: 'placeholder' },
            {
              type: 'playlist',
              meta: { height: 636 },
              elements: [{ id: '26', type: 'placeholder' }],
              direction: 'vertical'
            },
            { id: '28', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 179 } },
            { id: '30', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 259 } },
            { id: '32', type: 'placeholder' },
            { type: 'photo', meta: { height: 472 } },
            { id: '34', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 54 } },
            { id: '36', type: 'placeholder' },
            { type: 'subhead', meta: { length: 43 } },
            { id: '38', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 250 } },
            { id: '40', type: 'placeholder' },
            { type: 'photo', meta: { height: 472 } },
            { id: '42', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '44', type: 'placeholder' },
            { type: 'html', meta: { length: 406 } },
            { id: '46', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 137 } },
            { id: '48', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 110 } },
            { id: '50', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 288 } },
            { id: '52', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 83 } },
            { id: '54', type: 'placeholder' },
            { type: 'photo', meta: { height: 472 } },
            { id: '56', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 118 } },
            { id: '58', type: 'placeholder' },
            { type: 'photo', meta: { height: 472 } },
            { id: '60', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '62', type: 'placeholder' },
            { type: 'banner', meta: { height: 472 } },
            { id: '64', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '66', type: 'placeholder' },
            { type: 'social-embed', meta: { height: 360 } },
            { id: '68', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '69', type: 'placeholder' },
            {
              type: 'video',
              meta: { height: 472 },
              elements: [{ id: '73', type: 'placeholder' }],
              direction: 'vertical'
            },
            { id: '75', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '77', type: 'placeholder' },
            { type: 'teaser-gallery', meta: { height: 250 } },
            { id: '79', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '81', type: 'placeholder' },
            { type: 'quote', meta: { length: 61 } },
            { id: '83', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '85', type: 'placeholder' },
            { type: 'html', meta: { length: 402 } },
            { id: '87', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '89', type: 'placeholder' },
            { type: 'iframe', meta: { length: 87 } },
            { id: '91', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '93', type: 'placeholder' },
            { type: 'manhattan-widget', meta: { variant: 'weather-chart' } },
            { id: '95', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '97', type: 'placeholder' },
            { type: 'file', meta: { height: 40 } },
            { id: '99', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '101', type: 'placeholder' },
            { type: 'list', meta: {} },
            { id: '103', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '105', type: 'placeholder' },
            { type: 'list', meta: {} },
            { id: '107', type: 'placeholder' },
            { type: 'paragraph', meta: { length: 290 } },
            { id: '109', type: 'placeholder' },
            { type: 'teaser', meta: { height: 202 } },
            { id: '111', type: 'placeholder' }
          ],
          direction: 'vertical'
        },
        { id: '113', type: 'placeholder' },
        { type: 'author', meta: { length: 8 } },
        { id: '115', type: 'placeholder' },
        { type: 'source', meta: { length: 14 } },
        { id: '116', type: 'placeholder' },
        {
          type: 'recommendation-queue',
          elements: [
            { id: '118', type: 'placeholder' },
            {
              type: 'teaser-grid-module',
              meta: { variant: 'GRID_4_3' },
              elements: [
                { id: '120', type: 'placeholder' },
                { type: 'teaser' },
                { id: '122', type: 'placeholder' },
                { type: 'teaser' },
                { id: '124', type: 'placeholder' },
                { type: 'teaser' },
                { id: '126', type: 'placeholder' },
                { type: 'teaser' },
                { id: '128', type: 'placeholder' },
                { type: 'teaser' },
                { id: '130', type: 'placeholder' },
                { type: 'teaser' },
                { id: '131', type: 'placeholder' }
              ],
              direction: 'vertical'
            },
            { id: '132', type: 'placeholder' },
            {
              type: 'teaser-grid-module',
              meta: { variant: 'GRID_4_3' },
              elements: [
                { id: '134', type: 'placeholder' },
                { type: 'teaser' },
                { id: '136', type: 'placeholder' },
                { type: 'teaser' },
                { id: '138', type: 'placeholder' },
                { type: 'teaser' },
                { id: '140', type: 'placeholder' },
                { type: 'teaser' },
                { id: '142', type: 'placeholder' },
                { type: 'teaser' },
                { id: '144', type: 'placeholder' },
                { type: 'teaser' },
                { id: '145', type: 'placeholder' }
              ],
              direction: 'vertical'
            },
            { id: '147', type: 'placeholder' }
          ],
          direction: 'vertical'
        },
        { id: '148', type: 'placeholder' },
        {
          type: 'column-narrow',
          elements: [
            { id: '150', type: 'placeholder' },
            { type: 'latest-teasers', elements: [], direction: 'vertical' },
            { id: '152', type: 'placeholder' },
            { type: 'manhattan-widget', elements: [], direction: 'vertical' },
            { id: '154', type: 'placeholder' },
            { type: 'banner-html', elements: [], direction: 'vertical' },
            { id: '157', type: 'placeholder' }
          ],
          direction: 'vertical'
        },
        { id: '159', type: 'placeholder' }
      ],
      direction: 'vertical'
    },
    { id: '161', type: 'placeholder' },
    { type: 'footer' },
    { id: '163', type: 'placeholder' }
  ],
  direction: 'vertical'
};

const body_2_copy = cloneDeep(body_2);
body_2_copy.meta.deviceType = AdConfigDeviceTypeEnum.DESKTOP;

export const body_3: CommonRequest = body_2_copy;

const body_3_copy = cloneDeep(body_3);
body_3_copy.meta.deviceType = AdConfigDeviceTypeEnum.TABLET;
export const body_4: CommonRequest = body_3_copy;

export const body_3_AnyFact = {
  ...body_1,
  meta: {
    ...body_1.meta,
    locationInfoSectionId: 'AnyFact',
    deviceType: AdConfigDeviceTypeEnum.TABLET
  }
};
