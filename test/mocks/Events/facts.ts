import { FactType } from 'InterfacesAndTypes';

export const facts = [
  { id: '1', type: 'placeholder' },
  { id: '2', type: 'paragraph' },
  { id: '3', type: 'placeholder' },
  { id: '4', type: 'paragraph' },
  { id: '5', type: 'placeholder' },
  { id: '6', type: 'paragraph' },
  { id: '7', type: 'placeholder' },
  { id: '8', type: 'paragraph' },
  { id: '9', type: 'placeholder' },
  { id: '10', type: 'photo' },
  { id: '11', type: 'placeholder' },
  { id: '12', type: 'paragraph' },
  { id: '13', type: 'placeholder' },
  { id: '14', type: 'paragraph' },
  { id: '15', type: 'placeholder' },
  { id: '16', type: 'paragraph' },
  { id: '17', type: 'placeholder' },
  { id: '18', type: 'paragraph' },
  { id: '19', type: 'placeholder' },
  { id: '20', type: 'paragraph' },
  { id: '21', type: 'placeholder' },
  { id: '22', type: 'html' },
  { id: '23', type: 'placeholder' },
  { id: '24', type: 'paragraph' },
  { id: '25', type: 'placeholder' },
  { id: '26', type: 'paragraph' },
  { id: '27', type: 'placeholder' },
  { id: '28', type: 'html' },
  { id: '29', type: 'placeholder' },
  { id: '30', type: 'photo' },
  { id: '31', type: 'placeholder' },
  { id: '32', type: 'paragraph' },
  { id: '33', type: 'placeholder' },
  { id: '34', type: 'paragraph' },
  { id: '35', type: 'placeholder' }
] as FactType[];

export const facts2 = [
  { id: '1', type: 'placeholder' },
  { type: 'summary' },
  { id: '2', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '3', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '4', type: 'placeholder' },
  { type: 'subhead' },
  { id: '5', type: 'placeholder' },
  { type: 'list' },
  { id: '6', type: 'placeholder' },
  { type: 'manhattan-widget' },
  { id: '7', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '8', type: 'placeholder' },
  { type: 'manhattan-widget' },
  { id: '9', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '10', type: 'placeholder' },
  { type: 'subhead' },
  { id: '11', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '12', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '13', type: 'placeholder' },
  { type: 'subhead' },
  { id: '14', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '15', type: 'placeholder' },
  { type: 'banner' },
  { id: '16', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '17', type: 'placeholder' },
  { type: 'teaser' },
  { id: '18', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '19', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '20', type: 'placeholder' },
  { type: 'subhead' },
  { id: '21', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '22', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '23', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '24', type: 'placeholder' },
  { type: 'teaser' },
  { id: '25', type: 'placeholder' },
  { type: 'paragraph' },
  { id: '26', type: 'placeholder' }
] as FactType[];
