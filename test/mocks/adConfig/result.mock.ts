import { expectedResultFactory } from 'TestUtils';
import { AdConfigDeviceTypeEnum } from 'ads-layouts-tools';

const { DESKTOP, SMARTPHONE, TABLET } = AdConfigDeviceTypeEnum;

export const expectedResult_1 = expectedResultFactory({
  configName: 'main_page',
  placeholders: [{ deviceType: [DESKTOP], id: 'ID1' }]
});
export const expectedResult_2 = expectedResultFactory({
  configName: 'column',
  placeholders: [{ deviceType: [TABLET], id: 'ID3' }]
});

export const expectedResult_3 = expectedResultFactory({
  configName: 'gallery',
  placeholders: [{ deviceType: [SMARTPHONE], id: 'ID4' }]
});

export const expectedResult_4 = expectedResultFactory({
  configName: 'section',
  placeholders: [
    { id: 'ID1', deviceType: [DESKTOP] },
    { id: 'ID2', deviceType: [DESKTOP] },
    { id: 'ID3', deviceType: [DESKTOP] }
  ]
});
