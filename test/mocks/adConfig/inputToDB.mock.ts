import { AdConfig, AdConfigDeviceTypeEnum } from 'ads-layouts-tools';
import { adConfigFactory, PlaceholderForTests } from 'TestUtils';

const { DESKTOP, SMARTPHONE, TABLET } = AdConfigDeviceTypeEnum;

const placeholderArray1: PlaceholderForTests[] = [
  { id: 'ID1', deviceType: [DESKTOP] },
  { id: 'ID3', deviceType: [TABLET] },
  { id: 'ID4', deviceType: [SMARTPHONE] }
];

const placeholderArray2: PlaceholderForTests[] = [
  { id: 'ID1', deviceType: [DESKTOP], enabled: false },
  { id: 'ID3', deviceType: [TABLET], enabled: false },
  { id: 'ID4', deviceType: [SMARTPHONE], enabled: false }
];

export const adConfigMock: AdConfig[] = [
  adConfigFactory({
    releaseServices: ['ddtvn'],
    releaseVersion: 'release/1.52.0',
    config_name: 'main_page',
    pageType: ['main_page', 'main_page_mourning_light'],
    serviceId: ['biznes'],
    pageId: ['7537055'],
    section: [{ id: '111', name: 'main_page' }],
    placeholders: placeholderArray1
  }),
  adConfigFactory({
    releaseServices: ['biznes'],
    releaseVersion: 'release/1.44.0',
    config_name: 'column',
    pageType: ['column'],
    serviceId: ['biznes'],
    section: [{ id: '222', name: 'column' }],
    placeholders: placeholderArray1
  }),
  adConfigFactory({
    releaseServices: ['biznes'],
    releaseVersion: 'release/1.44.3',
    config_name: 'gallery',
    pageType: ['gallery'],
    serviceId: ['biznes'],
    placeholders: placeholderArray1
  }),
  adConfigFactory({
    releaseServices: ['zonymiami'],
    releaseVersion: 'release/1.42.0',
    config_name: 'report_story',
    pageType: [
      'report_report_descriptive',
      'report_report_descriptive_mourning_full',
      'report_report_descriptive_mourning_light'
    ],
    serviceId: ['zony_miami'],
    placeholders: placeholderArray2
  }),
  adConfigFactory({
    releaseServices: ['service1'],
    releaseVersion: 'release/2.52.0',
    config_name: 'section',
    pageType: ['section'],
    serviceId: ['service1'],
    pageId: ['7537055'],
    section: [{ id: '111', name: 'section' }],
    placeholders: [
      { id: 'ID1', deviceType: [DESKTOP] },
      { id: 'ID2', deviceType: [DESKTOP] },
      { id: 'ID3', deviceType: [DESKTOP] },
      { id: 'superpanel', deviceType: [SMARTPHONE] }
    ]
  })
];
