import { PlaceholdersDetails } from 'ads-layouts-tools';
import { IFilteredAdConfigs } from 'InterfacesAndTypes';
import { placeholderFactory } from 'TestUtils';

export const expectedResultFactory = ({
  configName,
  placeholders
}: {
  configName: IFilteredAdConfigs['configName'];
  placeholders: Pick<PlaceholdersDetails, 'deviceType' | 'id'>[];
}) => ({
  commonConfigFields: {
    activationThresholds: { delay: 0, offset: null, percent: null },
    bgPlugSrc: '',
    masterId: '',
    trafficCategory: []
  },
  configName,
  placeholders: placeholders.map(placeholderFactory)
});
