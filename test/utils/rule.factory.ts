import { AccessModelEnum, AdConfigDeviceTypeEnum, PaywallEnum, Rule } from 'ads-layouts-tools';
import { PageTypeEnum } from 'InterfacesAndTypes';
import { Types } from 'mongoose';

const { ObjectId } = Types;

export interface RuleLike extends Omit<Rule, 'conditions' | 'event'> {
  conditions: Types.ObjectId;
  event: Types.ObjectId;
}

export interface RuleLikeFactoryInput {
  name: string;
  conditionsName: string;
  conditions: string;
  eventName: string;
  event: string;
  serviceId: string[];

  pageType?: string[];
  locationInfoPageType?: string[];
  layout?: string[];
  enabled?: boolean;
  sectionId?: string[];
  pageId?: string[];
  priority?: number;
  siteVersion?: string[];
  rulesPackage?: string;
  paywall?: PaywallEnum[];
  accessModel?: AccessModelEnum[];
}

export class RuleLikeFactory implements RuleLike {
  name: string;
  pageType: string[];
  locationInfoPageType?: string[];
  serviceId: string[];
  layout: string[];
  enabled: boolean;
  sectionId?: string[];
  pageId?: string[];
  conditionsName: string;
  conditions: Types.ObjectId;
  eventName: string;
  event: Types.ObjectId;
  priority: number;
  siteVersion?: string[];
  rulesPackage?: string;
  paywall?: PaywallEnum[];
  accessModel?: AccessModelEnum[];

  constructor(params: RuleLikeFactoryInput) {
    this.name = params.name;
    this.conditionsName = params.conditionsName;
    this.conditions = new ObjectId(params.conditions);
    this.eventName = params.eventName;
    this.event = new ObjectId(params.event);
    this.serviceId = params.serviceId;

    this.pageType = params.pageType ?? [PageTypeEnum.article];
    this.locationInfoPageType = params.locationInfoPageType ?? [];
    this.layout = params.layout ?? Object.values(AdConfigDeviceTypeEnum);
    this.enabled = params.enabled ?? true;
    this.sectionId = params.sectionId ?? [];
    this.pageId = params.pageId ?? [];
    this.priority = params.priority ?? 1;
    this.siteVersion = params.siteVersion ?? [];
    this.rulesPackage = params.rulesPackage;
    this.paywall = params.paywall;
    this.accessModel = params.accessModel;
  }
}
