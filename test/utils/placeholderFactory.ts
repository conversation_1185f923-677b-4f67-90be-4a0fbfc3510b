import { AdConfigAdServerEnum, PlaceholdersDetails } from 'ads-layouts-tools';

export type PlaceholderForTests = Pick<PlaceholdersDetails, 'id' | 'deviceType'> &
  Partial<
    Pick<
      PlaceholdersDetails,
      'enabled' | 'AD_Config_group' | 'AD_Config_element_id' | 'width' | 'height'
    >
  >;

export const placeholderFactory = ({
  id,
  deviceType,
  enabled = true,
  AD_Config_group = '',
  AD_Config_element_id = '',
  width = '',
  height = ''
}: PlaceholderForTests): PlaceholdersDetails => ({
  id,
  deviceType,
  enabled,
  AD_Config_group,
  AD_Config_element_id,
  width,
  height,

  // defaults
  adSlots: [
    { adServer: AdConfigAdServerEnum.ADOCEAN },
    { adServer: AdConfigAdServerEnum.GAM }
  ],
  adServer: AdConfigAdServerEnum.GAM,
  code: '',
  bidders: [],
  mediaTypes: { banner: [] },
  activationThresholds: { offset: null, percent: null, delay: 0 }
});
