import { <PERSON><PERSON>, CACHE_MANAGER } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { CachePartsEnum, DisplayConfigDocument } from 'ads-layouts-tools';
import {
  AppCacheKeyType,
  CacheID,
  cachePrefix,
  DisplayConfigKey,
  ExtensionKey,
  KeyToDataType
} from 'InterfacesAndTypes';
import { CacheModule } from 'src/cacheModule/cache.module';
import { CacheService } from 'src/cacheModule/cache.service';
import { sha1 } from 'utility';

jest.mock('Logger/logger');

let cacheService: CacheService;
let cacheRef: jest.Mocked<Cache>;
let cacheServiceGetSpy: jest.SpyInstance;
let cacheServiceSetSpy: jest.SpyInstance;
let cacheServiceDelSpy: jest.SpyInstance;
let cacheRefGetSpy: jest.SpyInstance;
let cacheRefSetSpy: jest.SpyInstance;
let cacheRefDelSpy: jest.SpyInstance;

const cacheInput = [{ key: 'value' }] as unknown as DisplayConfigDocument[];

const key: DisplayConfigKey = 'RELEASE__ID__TIME';
const cacheId: CacheID = `${key}__${sha1(key)}`;

describe('cache Service test suite', () => {
  beforeEach(() => {
    cacheServiceGetSpy = jest.spyOn(cacheService, 'get');
    cacheServiceSetSpy = jest.spyOn(cacheService, 'set');
    cacheServiceDelSpy = jest.spyOn(cacheService, 'del');

    cacheRefGetSpy = jest.spyOn(cacheRef, 'get');
    cacheRefSetSpy = jest.spyOn(cacheRef, 'set');
    cacheRefDelSpy = jest.spyOn(cacheRef, 'del');
  });

  afterEach(async () => {
    jest.clearAllMocks();

    await cacheService.resetStore();
  });

  describe('cache ENABLED', () => {
    beforeAll(async () => {
      const app: TestingModule = await Test.createTestingModule({
        imports: [CacheModule.register({ isActive: true })]
      }).compile();

      cacheService = app.get(CacheService);
      cacheRef = app.get(CACHE_MANAGER);
    });

    it('should be an object', () => {
      expect(typeof cacheService).toBe('object');
    });

    it('should return cached value', async () => {
      cacheRefGetSpy.mockResolvedValueOnce(cacheInput);

      expect(await cacheService.get(key)).toBe(cacheInput);

      expect(cacheServiceGetSpy).toHaveBeenCalledWith(key);
      expect(cacheRefGetSpy).toHaveBeenCalledWith(cacheId);
    });

    it('should return null', async () => {
      cacheRefGetSpy.mockResolvedValueOnce(null);

      expect(await cacheService.get(key)).toBe(null);

      expect(cacheServiceGetSpy).toHaveBeenCalledWith(key);
      expect(cacheRefGetSpy).toHaveBeenCalledWith(cacheId);
    });

    it('should set value in cache', async () => {
      cacheRefSetSpy.mockResolvedValueOnce(cacheInput);

      await cacheService.set(key, cacheInput, false, 100);

      expect(cacheServiceSetSpy).toHaveBeenCalledWith(key, cacheInput, false, 100);
      expect(cacheRefSetSpy).toHaveBeenCalledWith(cacheId, cacheInput, 100);
    });

    it('should delete value from cache', async () => {
      cacheRefDelSpy.mockResolvedValueOnce(null);

      await cacheService.del(key);

      expect(cacheServiceDelSpy).toHaveBeenCalledWith(key);
      expect(cacheRefDelSpy).toHaveBeenCalledWith(cacheId);
    });
  });

  describe('cache DISABLED', () => {
    beforeAll(async () => {
      const app: TestingModule = await Test.createTestingModule({
        imports: [CacheModule.register({ isActive: false })]
      }).compile();

      cacheService = app.get(CacheService);
      cacheRef = app.get(CACHE_MANAGER);
    });

    it('should be an object', () => {
      expect(typeof cacheService).toBe('object');
    });

    it('should return cached value', async () => {
      await cacheService.get(key);

      expect(cacheServiceGetSpy).toHaveBeenCalledWith(key);
      expect(cacheRefGetSpy).not.toHaveBeenCalled();
    });

    it('should set value in cache', async () => {
      await cacheService.set(key, cacheInput, false, 100);

      expect(cacheServiceSetSpy).toHaveBeenCalledWith(key, cacheInput, false, 100);
      expect(cacheRefSetSpy).not.toHaveBeenCalled();
    });

    it('should delete value from cache', async () => {
      await cacheService.del(key);

      expect(cacheServiceDelSpy).toHaveBeenCalledWith(key);
      expect(cacheRefDelSpy).not.toHaveBeenCalled();
    });
  });

  describe('Cache - various edge cases', () => {
    beforeAll(async () => {
      const app: TestingModule = await Test.createTestingModule({
        imports: [CacheModule.register({ isActive: true })]
      }).compile();

      cacheService = app.get(CacheService);
      cacheRef = app.get(CACHE_MANAGER);
    });

    describe(`test cache's keys`, () => {
      const testCacheKey = async function <K extends AppCacheKeyType>(
        cacheKey: K
      ): Promise<void> {
        it(`should handle cache with key ${cacheKey}`, async () => {
          const cacheInput = [{ key: 'value' }] as unknown as KeyToDataType[K];

          await cacheService.set(cacheKey, cacheInput);
          expect(cacheServiceSetSpy).toHaveBeenCalledWith(cacheKey, cacheInput);

          let returnedValue = await cacheService.get(cacheKey);
          expect(returnedValue).toStrictEqual(cacheInput);
          expect(cacheServiceGetSpy).toHaveBeenCalledWith(cacheKey);

          await cacheService.del(cacheKey);
          expect(cacheServiceDelSpy).toHaveBeenCalledWith(cacheKey);

          returnedValue = await cacheService.get(cacheKey);
          expect(returnedValue).toStrictEqual(null);
          expect(cacheServiceGetSpy).toHaveBeenCalledWith(cacheKey);
          expect(cacheServiceGetSpy).toHaveBeenCalledTimes(2);
        });
      };

      const keys: AppCacheKeyType[] = [
        `${CachePartsEnum.RELEASE}__ServiceID__TIME`,
        `${CachePartsEnum.AD_CONFIGS}__ServiceID__PageType__release`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__desktop__rulesPackage`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__tablet__rulesPackage`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__smartphone__rulesPackage`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__desktop`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__tablet`,
        `${CachePartsEnum.RULES}__ServiceID__PageType__smartphone`,
        `${CachePartsEnum.RULES_PACKAGE}__ServiceID`,
        `${CachePartsEnum.EXTENSION}__ServiceID`
      ];
      keys.forEach(async key => {
        await testCacheKey(key);
      });
    });

    it('should return null when cache key does not exist and cache is active', async () => {
      const nonExistentKey = 'non-existent-key' as unknown as AppCacheKeyType;
      const result = await cacheService.get(nonExistentKey);

      expect(cacheServiceGetSpy).toHaveBeenCalledWith(nonExistentKey);
      expect(result).toBeNull();
    });

    it('should normalize keys with special characters when generating cache ID', async () => {
      const specialCharKey = './././/.///./././././//./.' as unknown as AppCacheKeyType;
      const expectedNormalizedKey = new RegExp(`_{${specialCharKey.length}}__[a-z0-9]+`, 'i');

      await cacheService.get(specialCharKey);

      expect(cacheRefGetSpy).toHaveBeenCalledWith(
        expect.stringMatching(expectedNormalizedKey)
      );
    });

    it('should retrieve all keys from cache store', async () => {
      const keyArr: AppCacheKeyType[] = [
        `${CachePartsEnum.AD_CONFIGS}__ID1__Page1__Release1`,
        `${CachePartsEnum.EXTENSION}__ID2`,
        `${CachePartsEnum.RELEASE}__ID3__Time1`
      ];
      const mockValue = 'value' as any;
      await Promise.all(
        keyArr.map(key => {
          cacheService.set(key, mockValue);
        })
      );

      const result = await cacheService.getKeys();

      result.forEach(cacheId => {
        const key = cacheId.match(/^(?<key>.+)__\w+$/);
        expect(keyArr).toContain(key?.groups?.key);
      });
    });

    it('should return an empty array when no keys exist in the cache', async () => {
      const result = await cacheService.getKeys();

      expect(result).toEqual([]);
    });

    it('should delete data from cache when active', async () => {
      const key: ExtensionKey = `${CachePartsEnum.EXTENSION}__ID`;
      const value = 'value' as any;

      await cacheService.set(key, value);

      const result = await cacheService.get(key);
      expect(result).toEqual(value);

      await cacheService.del(key);

      const result2 = await cacheService.get(key);
      expect(result2).toBeNull();
    });

    it('should set and retrieve service IDs correctly', async () => {
      const serviceIds = ['id1', 'id2'];
      await cacheService.setServiceIds(serviceIds);

      expect(cacheRefSetSpy).toHaveBeenCalled();

      const retrievedServiceIds = await cacheService.getServiceIds();
      expect(retrievedServiceIds).toEqual(serviceIds);
    });

    it('should delete cache entries with the given prefix and return the count', async () => {
      const keyArr: DisplayConfigKey[] = [
        `${CachePartsEnum.RELEASE}__ID1__Time1`,
        `${CachePartsEnum.RELEASE}__ID1__Time2`,
        `${CachePartsEnum.RELEASE}__ID2__Time3`,
        `${CachePartsEnum.RELEASE}__ID2__Time4`
      ];
      const value = 'value';

      await Promise.all(
        keyArr.map(async key => {
          await cacheService.set(key, value as any);
        })
      );

      const prefixToDeleteBy: cachePrefix = `${CachePartsEnum.RELEASE}__ID1`;
      const deletedCount = await cacheService.deleteByPrefix(prefixToDeleteBy);
      expect(deletedCount).toBe(2);

      const result = await cacheService.getKeys();

      result.forEach(cacheId => {
        const key = cacheId.match(/^(?<key>.+)__\w+$/);
        expect(keyArr).toContain(key?.groups?.key);
      });
    });
  });

  describe('Cache Omitted', () => {
    const key = 'key' as AppCacheKeyType;
    const value = 'value' as any;
    const ttl = 0;
    const omitCache = true;

    beforeAll(async () => {
      const app: TestingModule = await Test.createTestingModule({
        imports: [CacheModule.register({ isActive: true })]
      }).compile();

      cacheService = app.get(CacheService);
      cacheRef = app.get(CACHE_MANAGER);
    });

    it('should omit getting from cache on demand', async () => {
      await cacheService.set(key, value, false, ttl);

      const result = await cacheService.get(key, omitCache);
      expect(result).toBe(null);
    });

    it('should omit setting to cache on demand', async () => {
      await cacheService.set(key, value, omitCache, ttl);

      const result = await cacheService.get(key);
      expect(result).toBe(null);
    });
  });
});
