import { containsOfTypeOperator } from 'src/generator/operators';
import { getElementFromRequest } from 'Helpers';
import { body_1 } from 'Mocks';

describe('containsOfTypeOperator', () => {
  const { elements } = getElementFromRequest(body_1, 'storyline');

  it('should return true when facts count equals target count with equal rule', () => {
    const result = containsOfTypeOperator(elements, {
      count: 1,
      type: 'dashboard',
      rule: 'equal'
    });

    expect(result).toBe(true);
  });

  it('should return false when factValue is undefined', () => {
    const result = containsOfTypeOperator(undefined, {
      count: 1,
      type: 'paragraph',
      rule: 'equal'
    });

    expect(result).toBe(false);
  });

  it('should return true when facts count is less than or equal to the specified count for lessOrEqualTo rule', () => {
    const ruleConditionParams = {
      count: 3,
      type: 'subhead',
      rule: 'lessOrEqualTo'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when facts count is more than to the specified count for lessOrEqualTo rule', () => {
    const { elements } = getElementFromRequest(body_1, 'storyline');

    const ruleConditionParams = {
      count: 3,
      type: 'paragraph',
      rule: 'lessOrEqualTo'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return true when facts count is greater than target with "moreThan" rule', () => {
    const ruleConditionParams = {
      count: 4,
      type: 'paragraph',
      rule: 'moreThan'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when facts count is less or equal than target with "moreThan" rule', () => {
    const ruleConditionParams = {
      count: 10,
      type: 'paragraph',
      rule: 'moreThan'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return true when facts count is less than the specified count with "lessThan" rule', () => {
    const ruleConditionParams = {
      count: 10,
      type: 'paragraph',
      rule: 'lessThan'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when facts count is more than the specified count with "lessThan" rule', () => {
    const ruleConditionParams = {
      count: 2,
      type: 'paragraph',
      rule: 'lessThan'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return true when facts count is more or equal to the specified count with "moreOrEqualTo" rule', () => {
    const ruleConditionParams = {
      count: 6,
      type: 'paragraph',
      rule: 'moreOrEqualTo'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when facts count is less than the specified count with "moreOrEqualTo" rule', () => {
    const ruleConditionParams = {
      count: 7,
      type: 'paragraph',
      rule: 'moreOrEqualTo'
    };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return true when factsCount is more than or equal to the specified count and rule is not specified', () => {
    const ruleConditionParams = { count: 4, type: 'paragraph', rule: '' };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return true when factValue is empty and count is 0', () => {
    const ruleConditionParams = { count: 0, type: 'anyType', rule: '' };
    const result = containsOfTypeOperator([], ruleConditionParams);
    expect(result).toBe(true);
  });

  it('should return true when factValue is undefined and count is 0', () => {
    const ruleConditionParams = { count: 0, type: 'anyType', rule: '' };
    const result = containsOfTypeOperator(undefined, ruleConditionParams);
    expect(result).toBe(true);
  });

  it('should assign default rule if rule is unknown', () => {
    const ruleConditionParams = { count: 1, type: 'paragraph', rule: 'unknownRule' };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when no facts match the specified type', () => {
    const ruleConditionParams = { count: 1, type: 'video', rule: '' };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return false for negative count values', () => {
    const ruleConditionParams = { count: -1, type: 'placeholder', rule: '' };

    const result = containsOfTypeOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });
});
