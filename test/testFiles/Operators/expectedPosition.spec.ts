import { expectedPositionOperator } from 'src/generator/operators';
import { body_1 } from 'Mocks';
import { getElementFromRequest } from 'Helpers';

describe('expectedPositionOperator', () => {
  const { elements } = getElementFromRequest(body_1, 'column-wide');

  it('should return true when expected fact is at specified position and appears only once', () => {
    const params = {
      expectedFact: 'lead',
      position: 2,
      excludedFacts: [],
      expectOneFactOnly: true
    };

    const result = expectedPositionOperator(elements, params);

    expect(result).toBe(true);
  });

  it('should return false when elements is undefined', () => {
    const params = {
      expectedFact: 'photo',
      position: 1,
      excludedFacts: [],
      expectOneFactOnly: true
    };

    const result = expectedPositionOperator(undefined, params);

    expect(result).toBe(false);
  });

  it('should return false when elements is empty', () => {
    const ruleConditionParams = {
      expectedFact: 'someFact',
      position: 1,
      excludedFacts: [],
      expectOneFactOnly: true
    };

    const result = expectedPositionOperator([], ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return false when expectedFact is not found in elements', () => {
    const ruleConditionParams = {
      expectedFact: 'nonExistentType',
      position: 1,
      excludedFacts: [],
      expectOneFactOnly: true
    };

    const result = expectedPositionOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return false when position is out of bounds', () => {
    const ruleConditionParams = {
      expectedFact: 'news-list',
      position: elements!.length,
      excludedFacts: [],
      expectOneFactOnly: true
    };
    const result = expectedPositionOperator(elements, ruleConditionParams);
    expect(result).toBe(false);
  });

  it('should return false when multiple instances of expectedFact are found and expectOneFactOnly is true', () => {
    const { elements } = getElementFromRequest(body_1, 'storyline');

    const ruleConditionParams = {
      expectedFact: 'paragraph',
      position: 4,
      excludedFacts: [],
      expectOneFactOnly: true
    };

    const result = expectedPositionOperator(elements, ruleConditionParams);

    expect(result).toBe(false);
  });

  it('should return true when expected fact occurs at specified position and expectOneFactOnly is false', () => {
    const { elements } = getElementFromRequest(body_1, 'storyline');

    const ruleConditionParams = {
      expectedFact: 'paragraph',
      position: 4,
      excludedFacts: [],
      expectOneFactOnly: false
    };

    const result = expectedPositionOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });

  it('should return false when expected fact is found but not at the specified position', () => {
    const { elements } = getElementFromRequest(body_1, 'header');

    const ruleConditionParams = {
      expectedFact: 'title',
      position: 4,
      excludedFacts: [],
      expectOneFactOnly: false
    };
    const result = expectedPositionOperator(elements, ruleConditionParams);
    expect(result).toBe(false);
  });

  it('should return true when the expected fact is at the specified position and only one instance exists', () => {
    const { elements } = getElementFromRequest(body_1, 'header');

    const ruleConditionParams = {
      expectedFact: 'title',
      position: 2,
      excludedFacts: [],
      expectOneFactOnly: false
    };
    const result = expectedPositionOperator(elements, ruleConditionParams);
    expect(result).toBe(true);
  });

  it('should filter out excluded facts and verify position correctly', () => {
    const { elements } = getElementFromRequest(body_1, 'storyline');

    const ruleConditionParams = {
      expectedFact: 'paragraph',
      position: 3,
      excludedFacts: ['subhead'],
      expectOneFactOnly: false
    };

    const result = expectedPositionOperator(elements, ruleConditionParams);

    expect(result).toBe(true);
  });
});
