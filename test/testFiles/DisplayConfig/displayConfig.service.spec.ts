import { Test, TestingModule } from '@nestjs/testing';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { Connection, connect, Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';
import { CreateException } from 'src/errors/exceptions';
import { HttpStatus } from '@nestjs/common';
import { DisplayConfigKey, ReleaseWithTimestamp } from 'InterfacesAndTypes';
import { commonMockDisplayConfigData } from 'Mocks';
import { CacheModule } from 'src/cacheModule/cache.module';
import { DisplayConfig, DisplayConfigSchema } from 'ads-layouts-tools';

jest.mock('Logger/logger');

describe('DisplayConfig service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoDisplayConfigModel: Model<DisplayConfig>;
  let service: DisplayConfigService;
  let mongoTable: DisplayConfig[];

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoDisplayConfigModel = mongoConnection.model(DisplayConfig.name, DisplayConfigSchema);

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        DisplayConfigService,
        {
          provide: getModelToken(DisplayConfig.name),
          useValue: mongoDisplayConfigModel
        }
      ]
    }).compile();

    service = app.get(DisplayConfigService);
  });

  beforeEach(async () => {
    mongoTable = [
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'AFTER:2024-05-29T13:00:00',
        release: 'release/1.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'AFTER:2023-04-29T15:00:00',
        release: 'release/2.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'RELEASE',
        release: 'release/3.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'ab_atsdk_ga',
        release: 'release/4.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'ddtvn',
        siteVersion: 'ab_adph_ga,ab_cwv_gb',
        release: 'release/5.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn',
        release: 'release/6.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn',
        release: 'release/7.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'tvn24',
        release: 'release/8.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'default',
        release: 'release/9.0.0'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A',
        release: 'release/9.0.1'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'B',
        release: 'release/9.0.2'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A,B',
        release: 'release/9.0.3'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'B,C',
        release: 'release/9.0.4'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'A,B,C',
        release: 'release/9.0.5'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'D,E,F',
        release: 'release/9.0.6'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'D,E,G',
        release: 'release/9.0.7'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'H',
        release: 'release/9.0.8'
      },
      {
        ...commonMockDisplayConfigData,
        service: 'vod',
        siteVersion: 'I',
        release: 'release/9.0.9'
      }
    ];
    await mongoDisplayConfigModel.insertMany(mongoTable);
  });

  afterEach(async () => {
    await mongoDisplayConfigModel.deleteMany({});
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('DisplayConfigService', () => {
    describe('getUniqServiceIds', () => {
      it('should return uniq Service Ids', async () => {
        const uniqIds = await service.getUniqServiceIds();
        expect(uniqIds).toEqual(['ddtvn', 'tvn', 'tvn24', 'vod']);
      });

      it('should throw DisplayConfigs data not found!', async () => {
        await mongoDisplayConfigModel.deleteMany({});
        void expect(service.getUniqServiceIds()).rejects.toThrowError(
          CreateException({
            message: 'DisplayConfigs data not found!',
            statusCode: HttpStatus.NOT_FOUND
          })
        );
      });
    });

    describe('selectReleaseVersion', () => {
      const defaultServiceId = 'ddtvn';
      const defaultEnv = 'default';

      it('should return correct selectedRelease for serviceId', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: 'tvn',
          serviceEnv: defaultEnv,
          siteVersion: 'default',
          time: ''
        });

        expect(selectedVersion).toEqual('release/6.0.0');
      });

      it('should return default', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: ''
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // AFTER
      it('should select correct value for key AFTER:2024-05-29T13:00:00 for timestamp 29-05-2024 13:00:01', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1716980401000' // 29-05-2024 13:00:01
        });

        expect(selectedVersion).toEqual('release/1.0.0');
      });

      it('should select correct value for key AFTER:2023-04-29T15:00:00 for timestamp 29-04-2024 15:00:01', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1682773201000' // 29-04-2023 15:00:01
        });

        expect(selectedVersion).toEqual('release/2.0.0');
      });

      it("should select default value if cannot match 'AFTER' prefixed keys", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // default
      it('should select default value if serviceVersion explicitly equal default', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'default',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      it('should select default value if serviceVersion explicitly equal RELEASE', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'RELEASE',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      it('should select default value if serviceVersion and time not provided', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: ''
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // single
      it('should select correct value for single serviceVersion provided and AFTER key can match', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_atsdk_ga',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/4.0.0');
      });

      // multiple
      it('should select correct value for multiple serviceVersion provided and AFTER key can match', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,ab_cwv_gb',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/5.0.0');
      });

      it("should select correct value for multiple serviceVersion that don't match provided and AFTER key can match", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,ab_cwv_gb,foo',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/5.0.0');
      });

      it("should select correct value for multiple serviceVersion that don't match provided and AFTER key can match", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,foo',
          time: '1682769600000' // 29-04-2023 14:00:00
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });
    });

    describe('getReleaseForSiteVersions', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      beforeEach(async () => {
        allDisplayConfigsForEnv = await mongoDisplayConfigModel.find({});
      });

      describe('should return correct record', () => {
        it('should return first correct record given single specified siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'default');
          expect(result).toEqual('release/6.0.0');
        });

        it('should return correct record given multiple specified siteVersion', () => {
          const result = service.getReleaseForSiteVersions(
            allDisplayConfigsForEnv,
            'ab_adph_ga,ab_cwv_gb'
          );
          expect(result).toEqual('release/5.0.0');
        });

        it('should return exact match given single specified siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'A');
          expect(result).toEqual('release/9.0.1');
        });

        it('should return exact match given 2 keys for specified siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'A,B');
          expect(result).toEqual('release/9.0.3');
        });

        it('should return exact match given 3 keys for specified siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'A,B,C');
          expect(result).toEqual('release/9.0.5');
        });

        it('should return correct record without full overlap - 1 key', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'A,D');
          expect(result).toEqual('release/9.0.1');
        });

        it('should return correct record without full overlap - 2 keys', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'A,B,D');
          expect(result).toEqual('release/9.0.3');
        });

        it('should return first occurrence that match non sufficient siteVersion overlap - 1', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'H,I');
          expect(result).toEqual('release/9.0.8');
        });

        it('should return first occurrence that match non sufficient siteVersion overlap - 2', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'I,H');
          expect(result).toEqual('release/9.0.8');
        });
      });

      describe('should return undefined', () => {
        it('should return undefined given empty siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, '');
          expect(result).toEqual(undefined);
        });

        it('should return undefined given non existent siteVersion', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'foo,bar');
          expect(result).toEqual(undefined);
        });

        it('should return undefined non sufficient siteVersion overlap', () => {
          const result = service.getReleaseForSiteVersions(allDisplayConfigsForEnv, 'D,E');
          expect(result).toEqual(undefined);
        });
      });
    });

    describe('findConfigsWithSiteVersionAFTER', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      it('should return records sorted by timeStamp for specified siteVersion', async () => {
        allDisplayConfigsForEnv = await mongoDisplayConfigModel.find({});
        const result = service.findConfigsWithSiteVersionAFTER(allDisplayConfigsForEnv);
        const expected: ReleaseWithTimestamp[] = [
          { release: 'release/1.0.0', timestamp: 1716980400000 },
          { release: 'release/2.0.0', timestamp: 1682773200000 }
        ];
        expect(result).toEqual(expected);
      });

      it('should return undefined given if no config match regex pattern', () => {
        allDisplayConfigsForEnv = [];
        const result = service.findConfigsWithSiteVersionAFTER(allDisplayConfigsForEnv);
        expect(result).toEqual(undefined);
      });
    });

    describe('findConfigsAfterRequestTimestamp', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      beforeEach(async () => {
        allDisplayConfigsForEnv = await mongoDisplayConfigModel.find({});
      });
      it('should return correct release for specified timestamp', () => {
        const result = service.findConfigsAfterRequestTimestamp(
          allDisplayConfigsForEnv,
          '1700000000000'
        );
        expect(result).toEqual('release/2.0.0');
      });

      it('should return undefined given no config is before timestamp', () => {
        const result = service.findConfigsAfterRequestTimestamp(
          allDisplayConfigsForEnv,
          '1600000000000'
        );
        expect(result).toEqual(undefined);
      });
    });

    // TODO: add test for selectDisplayConfigs
    describe('selectDisplayConfigs', () => {
      it('should return correct value', async () => {
        const serviceId = 'tvn24';
        const serviceEnv = 'default';
        const releaseCacheId: DisplayConfigKey = 'RELEASE__ID__TIME';

        const [result, ...rest] = await service.selectDisplayConfigs(
          releaseCacheId,
          serviceId,
          serviceEnv
        );

        expect(rest.length).toEqual(0);
        expect(result.service).toEqual(serviceId);
        expect(result.release).toEqual('release/8.0.0');
      });

      it('should return correct list of configs', async () => {
        const serviceId = 'ddtvn';
        const serviceEnv = 'default';
        const releaseCacheId: DisplayConfigKey = 'RELEASE__ID__TIME';

        const result = await service.selectDisplayConfigs(
          releaseCacheId,
          serviceId,
          serviceEnv
        );

        expect(result.length).toEqual(5);
      });
    });
  });
});
