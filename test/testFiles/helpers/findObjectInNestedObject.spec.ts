import { findObjectInNestedObject } from 'Helpers';

describe('findObjectInNestedObject test suite', () => {
  const output = [
    {
      id: 'slot-storyline-1',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 212
      }
    },
    {
      id: 'slot-storyline-2',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 50
      }
    },
    {
      id: 'slot-storyline-3',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 212
      }
    },
    {
      id: 'slot-storyline-4',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 90
      }
    },
    {
      id: 'slot-storyline-5',
      type: 'placeholder'
    },
    {
      type: 'subhead',
      meta: {
        length: 70
      }
    },
    {
      id: 'slot-storyline-6',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 70
      }
    },
    {
      id: 'slot-storyline-7',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 70
      }
    },
    {
      id: 'slot-storyline-8',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 175
      }
    },
    {
      id: 'slot-storyline-9',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 70
      }
    },
    {
      id: 'slot-storyline-10',
      type: 'placeholder'
    },
    {
      type: 'photo',
      meta: {
        height: 202
      }
    },
    {
      id: 'slot-storyline-11',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 175
      }
    },
    {
      id: 'slot-storyline-12',
      type: 'placeholder'
    },
    {
      type: 'paragraph',
      meta: {
        length: 70
      }
    },
    {
      id: 'slot-storyline-13',
      type: 'placeholder'
    },
    {
      type: 'map',
      meta: {
        height: 202
      }
    },
    {
      id: 'slot-storyline-14',
      type: 'placeholder'
    },
    {
      type: 'widget',
      meta: {
        variant: 'tvn24go-embed'
      }
    },
    {
      id: 'slot-storyline-15',
      type: 'placeholder'
    },
    {
      type: 'author',
      meta: {
        length: 25
      }
    },
    {
      id: 'slot-storyline-16',
      type: 'placeholder'
    },
    {
      type: 'source',
      meta: {
        length: 25
      }
    },
    {
      id: 'slot-storyline-17',
      type: 'placeholder'
    },
    {
      type: 'source',
      meta: {
        length: 25
      }
    },
    {
      id: 'slot-storyline-18',
      type: 'placeholder'
    }
  ];

  const input = {
    type: 'article',
    meta: {
      device: 'desktop'
    },
    elements: [
      {
        id: 'slot-article-1',
        type: 'placeholder'
      },
      {
        type: 'top-menu'
      },
      {
        id: 'slot-article-2',
        type: 'placeholder'
      },
      {
        type: 'main',
        elements: [
          {
            id: 'slot-main-1',
            type: 'placeholder'
          },
          {
            type: 'head',
            elements: [
              {
                id: 'slot-head-1',
                type: 'placeholder'
              },
              {
                type: 'title',
                meta: {
                  length: 255
                }
              },
              {
                id: 'slot-head-2',
                type: 'placeholder'
              },
              {
                type: 'meta',
                meta: {
                  height: 40
                }
              },
              {
                id: 'slot-head-3',
                type: 'placeholder'
              },
              {
                type: 'main-multimedium',
                meta: {
                  height: 450
                }
              },
              {
                id: 'slot-head-4',
                type: 'placeholder'
              },
              {
                type: 'share',
                meta: {
                  height: 60
                }
              },
              {
                id: 'slot-head-5',
                type: 'placeholder'
              }
            ]
          },
          {
            id: 'slot-main-2',
            type: 'placeholder'
          },
          {
            type: 'lead',
            meta: {
              length: 333
            }
          },
          {
            id: 'slot-main-3',
            type: 'placeholder'
          },
          {
            type: 'columns',
            elements: [
              {
                type: 'column-wide',
                elements: [
                  {
                    id: 'slot-column-wide-1',
                    type: 'placeholder'
                  },
                  {
                    type: 'storyline',
                    elements: output
                  },
                  {
                    id: 'slot-column-wide-2',
                    type: 'placeholder'
                  },
                  {
                    type: 'share',
                    meta: {
                      height: 60
                    }
                  },
                  {
                    id: 'slot-column-wide-3',
                    type: 'placeholder'
                  },
                  {
                    type: 'tags',
                    meta: {
                      height: 60
                    }
                  },
                  {
                    id: 'slot-column-wide-4',
                    type: 'placeholder'
                  },
                  {
                    type: 'news-list',
                    elements: [
                      {
                        id: 'slot-news-list-1',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 450
                        }
                      },
                      {
                        id: 'slot-news-list-2',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 450
                        }
                      },
                      {
                        id: 'slot-news-list-3',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 450
                        }
                      },
                      {
                        id: 'slot-news-list-4',
                        type: 'placeholder'
                      }
                    ]
                  },
                  {
                    id: 'slot-column-wide-5',
                    type: 'placeholder'
                  }
                ]
              },
              {
                type: 'column-narrow',
                elements: [
                  {
                    id: 'slot-column-narrow-1',
                    type: 'placeholder'
                  },
                  {
                    type: 'related',
                    elements: [
                      {
                        id: 'slot-related-1',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 200
                        }
                      },
                      {
                        id: 'slot-related-2',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 200
                        }
                      },
                      {
                        id: 'slot-related-3',
                        type: 'placeholder'
                      },
                      {
                        type: 'teaser',
                        meta: {
                          height: 200
                        }
                      },
                      {
                        id: 'slot-related-4',
                        type: 'placeholder'
                      }
                    ]
                  },
                  {
                    id: 'slot-column-narrow-2',
                    type: 'placeholder'
                  }
                ]
              }
            ]
          },
          {
            id: 'slot-main-4',
            type: 'placeholder'
          }
        ]
      },
      {
        id: 'slot-article-3',
        type: 'placeholder'
      },
      {
        type: 'footer'
      },
      {
        id: 'slot-article-4',
        type: 'placeholder'
      }
    ]
  };

  test('is a function', () => {
    expect(typeof findObjectInNestedObject).toBe('function');
  });

  test('correctly extracts field form object of given type', () => {
    expect(findObjectInNestedObject(input, 'type', 'storyline', 'elements')).toEqual(output);
  });
});
