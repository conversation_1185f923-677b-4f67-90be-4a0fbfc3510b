import { isStringArray } from 'Helpers';

describe('isStringArray', () => {
  it('should return true when given an array of strings', () => {
    const result = isStringArray(['hello', 'world']);
    expect(result).toBe(true);
  });

  it('should return false when given an empty array', () => {
    const result = isStringArray([]);
    expect(result).toBe(false);
  });

  it('should return false when given an array of non-strings', () => {
    const result = isStringArray([1, true, null]);
    expect(result).toBe(false);
  });

  it('should return false when given a non-array argument', () => {
    const result = isStringArray('hello');
    expect(result).toBe(false);
  });
});
