import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AdConfigDeviceTypeEnum,
  Event,
  PlaceholderPositionEnum,
  VariantWeightsConfig,
  VariantWeightsConfigSchema
} from 'ads-layouts-tools';
import { FactType } from 'InterfacesAndTypes';
import { facts, variantConfigDummy } from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { CacheModule } from 'src/cacheModule/cache.module';
import { EventsService } from 'src/events/events.service';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Logger/logger');

describe('getEveryPositionPlaceholder test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let service: EventsService;
  let mongoVariantsConfigModel: Model<VariantWeightsConfig>;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoVariantsConfigModel = mongoConnection.model(
      VariantWeightsConfig.name,
      VariantWeightsConfigSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        EventsService,
        VariantWeightsConfigService,
        {
          provide: getModelToken(VariantWeightsConfig.name),
          useValue: mongoVariantsConfigModel
        }
      ]
    }).compile();

    service = app.get(EventsService);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('getEveryPositionPlaceholder', () => {
    describe('placeholderPositions', () => {
      it('should return filtered placeholders when placeholderPositions array is provided', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 3, 10, 15],
              placeholderType: 'placeholder'
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '1', type: 'placeholder' },
          { id: '5', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '29', type: 'placeholder' }
        ]);
      });

      it('should filter placeholders by specified column-placeholder', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 3],
              placeholderType: 'column-placeholder'
            }
          }
        } as Event;

        const facts = [
          { id: '1', type: 'placeholder' },
          { id: '2', type: 'text' },
          { id: '3', type: 'column-placeholder' },
          { id: '4', type: 'text' },
          { id: '5', type: 'placeholder' },
          { id: '6', type: 'text' },
          { id: '7', type: 'column-placeholder' },
          { id: '8', type: 'text' },
          { id: '9', type: 'placeholder' },
          { id: '10', type: 'text' },
          { id: '11', type: 'column-placeholder' }
        ] as FactType[];

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '3', type: 'column-placeholder' },
          { id: '11', type: 'column-placeholder' }
        ]);
      });

      it('should not include first placeholder when "last" is in placeholderPositions and countBackwards is true', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 4, 9, 'last'],
              placeholderType: 'placeholder',
              countBackwards: true
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '35', type: 'placeholder' },
          { id: '29', type: 'placeholder' },
          { id: '19', type: 'placeholder' }
        ]);
      });

      it('should return the last placeholder when "last" is in placeholderPositions and countBackwards is false', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: ['last'],
              countBackwards: false
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([{ id: '35', type: 'placeholder' }]);
      });

      it('should return an empty array when placeholder positions are out of range', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [100, 200],
              placeholderType: 'column-placeholder'
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([]);
      });

      it('should return an empty array when placeholder positions are out of range', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [100, 200],
              placeholderType: 'placeholder'
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([]);
      });
    });

    describe('elementsPositions', () => {
      it('should return selected placeholders based on elementsPositions', async () => {
        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [1, PlaceholderPositionEnum.UNDER],
                [3, PlaceholderPositionEnum.ABOVE]
              ]
            }
          }
        } as Event;

        const facts = [
          { id: '1', type: 'text' },
          { id: '2', type: 'placeholder' },
          { id: '3', type: 'text' },
          { id: '4', type: 'placeholder' },
          { id: '5', type: 'text' }
        ] as FactType[];

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '2', type: 'placeholder' },
          { id: '4', type: 'placeholder' }
        ]);
      });

      it('should return reversed placeholders for placeholderPositions when countBackwards is true', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 2, 3],
              countBackwards: true
            }
          }
        } as Event;

        const facts = [
          { id: '1', type: 'placeholder' },
          { id: '2', type: 'text' },
          { id: '3', type: 'placeholder' },
          { id: '4', type: 'text' },
          { id: '5', type: 'placeholder' }
        ] as FactType[];

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '5', type: 'placeholder' },
          { id: '3', type: 'placeholder' },
          { id: '1', type: 'placeholder' }
        ]);
      });

      it('should return reversed placeholders when countBackwards is true', () => {
        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [1, PlaceholderPositionEnum.ABOVE],
                [3, PlaceholderPositionEnum.UNDER],
                [5, PlaceholderPositionEnum.UNDER]
              ],
              countBackwards: true
            }
          }
        } as unknown as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '35', type: 'placeholder' },
          { id: '29', type: 'placeholder' },
          { id: '25', type: 'placeholder' }
        ]);
      });

      it('should return correct placeholders when position is UNDER', () => {
        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [1, PlaceholderPositionEnum.UNDER],
                [2, PlaceholderPositionEnum.UNDER],
                [3, PlaceholderPositionEnum.UNDER],
                [4, PlaceholderPositionEnum.UNDER]
              ],
              countBackwards: false
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '3', type: 'placeholder' },
          { id: '5', type: 'placeholder' },
          { id: '7', type: 'placeholder' },
          { id: '9', type: 'placeholder' }
        ]);
      });

      it('should return correct placeholders when position is ABOVE', () => {
        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [1, PlaceholderPositionEnum.ABOVE],
                [2, PlaceholderPositionEnum.ABOVE],
                [3, PlaceholderPositionEnum.ABOVE],
                [4, PlaceholderPositionEnum.ABOVE]
              ],
              countBackwards: false
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '1', type: 'placeholder' },
          { id: '3', type: 'placeholder' },
          { id: '5', type: 'placeholder' },
          { id: '7', type: 'placeholder' }
        ]);
      });

      it('should include variants correctly', async () => {
        await mongoVariantsConfigModel.insertMany(variantConfigDummy);

        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [2, PlaceholderPositionEnum.UNDER],
                [12, PlaceholderPositionEnum.UNDER],
                [32, PlaceholderPositionEnum.UNDER]
              ],
              enableVariant: true
            }
          }
        } as Event;

        const facts = [
          { id: '1', type: 'placeholder' },
          { id: '2', type: 'moduleTestName', meta: { variant: 'AAA' } },
          { id: '3', type: 'placeholder' },
          { id: '4', type: 'otherModuleTestName', meta: { variant: 'BBB' } },
          { id: '5', type: 'placeholder' },
          { id: '6', type: 'moduleTestName', meta: { variant: 'CCC' } },
          { id: '7', type: 'placeholder' }
        ] as FactType[];

        await service.updateVariantWeights();
        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '3', type: 'placeholder' },
          { id: '5', type: 'placeholder' },
          { id: '7', type: 'placeholder' }
        ]);

        await mongoVariantsConfigModel.deleteMany({});
      });

      it('should include variants correctly, including modules with weight=0', async () => {
        await mongoVariantsConfigModel.insertMany(variantConfigDummy);

        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [10, PlaceholderPositionEnum.UNDER],
                [30, PlaceholderPositionEnum.UNDER]
              ],
              enableVariant: true
            }
          }
        } as Event;

        const facts = [
          { id: '1', type: 'placeholder' },
          { id: '2', type: 'file', meta: { variant: 'AAA' } },
          { id: '3', type: 'placeholder' },
          { id: '4', type: 'otherModuleTestName', meta: { variant: 'BBB' } },
          { id: '5', type: 'placeholder' },
          { id: '6', type: 'moduleTestName', meta: { variant: 'CCC' } },
          { id: '7', type: 'placeholder' }
        ] as FactType[];

        await service.updateVariantWeights();
        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([
          { id: '5', type: 'placeholder' },
          { id: '7', type: 'placeholder' }
        ]);

        await mongoVariantsConfigModel.deleteMany({});
      });
    });

    describe('edge cases', () => {
      it('should return empty array when placeholderPositions and elementsPositions are undefined', () => {
        const event = {
          params: {
            placeholder: {}
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([]);
      });

      it('should return an empty array when fact array is empty', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 2, 3]
            }
          }
        } as Event;

        const facts: FactType[] = [];
        const deviceType = AdConfigDeviceTypeEnum.DESKTOP;

        const result = service.getEveryPositionPlaceholder(event, facts, deviceType);

        expect(result).toStrictEqual([]);
      });

      it('should return an empty array when no placeholders match the filter criteria', () => {
        const event = {
          params: {
            placeholder: {
              placeholderPositions: [1, 2, 3],
              placeholderType: 'column-placeholder'
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([]);
      });

      it('should return an empty array when any index is not found', () => {
        const event = {
          params: {
            placeholder: {
              elementsPositions: [
                [0, PlaceholderPositionEnum.ABOVE],
                [facts.length, PlaceholderPositionEnum.UNDER]
              ]
            }
          }
        } as Event;

        const result = service.getEveryPositionPlaceholder(
          event,
          facts,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toStrictEqual([]);
      });
    });
  });
});
