import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AdConfig,
  AdConfigSchema,
  Conditions,
  ConditionsSchema,
  DisplayConfig,
  DisplayConfigSchema,
  Event,
  EventSchema,
  ExtensionConfig,
  ExtensionConfigSchema,
  Rule,
  RuleSchema,
  ServiceToPackageMap,
  ServiceToPackageMapSchema,
  VariantWeightsConfig,
  VariantWeightsConfigSchema
} from 'ads-layouts-tools';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { AdConfigService } from 'src/adConfigs/adConfig.service';
import { CacheModule } from 'src/cacheModule/cache.module';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';
import { EventsService } from 'src/events/events.service';
import { ExtensionService } from 'src/extensionConfig/extension.service';
import { GeneratorController } from 'src/generator/generator.controller';
import { GeneratorModule } from 'src/generator/generator.module';
import { GeneratorService } from 'src/generator/generator.service';
import { RuleService } from 'src/rules/rules.service';
import { ServiceToPackageMapService } from 'src/serviceToPackageMap/serviceToPackageMap.service';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Logger/logger');

describe('GeneratorModule', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;

  let generatorController: GeneratorController;
  let generatorService: GeneratorService;
  let generatorModule: GeneratorModule;

  let mongoVariantsConfigModel: Model<VariantWeightsConfig>;
  let mongoAdConfigModel: Model<AdConfig>;
  let mongoDisplayConfigModel: Model<DisplayConfig>;
  let mongoExtensionConfigModel: Model<ExtensionConfig>;
  let mongoServiceToPackageMapModel: Model<ServiceToPackageMap>;
  let mongoEventsModel: Model<Event>;
  let mongoConditionsModel: Model<Conditions>;
  let mongoRuleModel: Model<Rule>;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoAdConfigModel = mongoConnection.model(AdConfig.name, AdConfigSchema);
    mongoConditionsModel = mongoConnection.model(Conditions.name, ConditionsSchema);
    mongoDisplayConfigModel = mongoConnection.model(DisplayConfig.name, DisplayConfigSchema);
    mongoEventsModel = mongoConnection.model(Event.name, EventSchema);
    mongoRuleModel = mongoConnection.model(Rule.name, RuleSchema);
    mongoExtensionConfigModel = mongoConnection.model(
      ExtensionConfig.name,
      ExtensionConfigSchema
    );
    mongoServiceToPackageMapModel = mongoConnection.model(
      ServiceToPackageMap.name,
      ServiceToPackageMapSchema
    );
    mongoVariantsConfigModel = mongoConnection.model(
      VariantWeightsConfig.name,
      VariantWeightsConfigSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      controllers: [GeneratorController],
      providers: [
        GeneratorModule,
        GeneratorService,
        AdConfigService,
        RuleService,
        DisplayConfigService,
        ExtensionService,
        ServiceToPackageMapService,
        VariantWeightsConfigService,
        EventsService,
        {
          provide: getModelToken(AdConfig.name),
          useValue: mongoAdConfigModel
        },
        {
          provide: getModelToken(DisplayConfig.name),
          useValue: mongoDisplayConfigModel
        },
        {
          provide: getModelToken(Event.name),
          useValue: mongoEventsModel
        },
        {
          provide: getModelToken(ExtensionConfig.name),
          useValue: mongoExtensionConfigModel
        },
        {
          provide: getModelToken(Rule.name),
          useValue: mongoRuleModel
        },
        {
          provide: getModelToken(ServiceToPackageMap.name),
          useValue: mongoServiceToPackageMapModel
        },
        {
          provide: getModelToken(VariantWeightsConfig.name),
          useValue: mongoVariantsConfigModel
        }
      ]
    }).compile();

    generatorModule = app.get(GeneratorModule);
    generatorController = app.get(GeneratorController);
    generatorService = app.get(GeneratorService);
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  it('should be defined', () => {
    expect(generatorModule).toBeDefined();
    expect(generatorController).toBeDefined();
    expect(generatorService).toBeDefined();
  });
});
