import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AdConfigDeviceTypeEnum,
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  Conditions,
  ConditionsSchema,
  Event,
  EventSchema,
  Rule,
  RuleSchema
} from 'ads-layouts-tools';
import { ContentMeta, RulesQueryArgs } from 'InterfacesAndTypes';
import { conditionsDB, eventsDB, rulesDB } from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { CacheModule } from 'src/cacheModule/cache.module';
import { RuleService } from 'src/rules/rules.service';

jest.mock('Logger/logger');

describe('Rule service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoRuleModel: Model<Rule>;
  let mongoConditionsModel: Model<Conditions>;
  let mongoEventsModel: Model<Event>;
  let service: RuleService;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoConditionsModel = mongoConnection.model(Conditions.name, ConditionsSchema);
    mongoConditionsModel.discriminator(AnyConditions.name, AnyConditionsSchema);
    mongoConditionsModel.discriminator(AllConditions.name, AllConditionsSchema);
    mongoEventsModel = mongoConnection.model(Event.name, EventSchema);
    mongoRuleModel = mongoConnection.model(Rule.name, RuleSchema);

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        RuleService,
        {
          provide: getModelToken(Conditions.name),
          useValue: mongoConditionsModel
        },
        {
          provide: getModelToken(Event.name),
          useValue: mongoEventsModel
        },
        {
          provide: getModelToken(Rule.name),
          useValue: mongoRuleModel
        }
      ]
    }).compile();

    service = app.get(RuleService);
  });

  beforeEach(async () => {
    await mongoConditionsModel.insertMany(conditionsDB);
    await mongoEventsModel.insertMany(eventsDB);
    await mongoRuleModel.insertMany(rulesDB);
  });

  afterEach(async () => {
    await mongoConditionsModel.deleteMany({});
    await mongoEventsModel.deleteMany({});
    await mongoRuleModel.deleteMany({});
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('Rule service', () => {
    const contentMeta: ContentMeta = {
      deviceType: AdConfigDeviceTypeEnum.DESKTOP,
      locationInfoPageType: 'story_story_video',
      locationInfoSectionId: '222',
      locationInfoSectionName: 'Gwiazdy',
      serviceEnv: 'production',
      serviceId: 'ddtvn',
      siteVersion: 'ab_atsdk_ga',
      time: '1716539400000',
      siteVersionIdentifier: '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12',
      locationInfoPageId: '7931689'
    };

    const args: RulesQueryArgs = {
      rulesPackage: undefined,
      type: 'article',
      contentMeta
    };
    describe('base cases', () => {
      it('should success', async () => {
        const response = await service.getPageRules(args);
        expect(response).toHaveLength(14);
      });

      it('should fail with not existing rulesPackage', async () => {
        const modifiedArgs: RulesQueryArgs = { ...args, rulesPackage: 'notExists' };

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });

      it('should fail with empty type', async () => {
        const modifiedArgs: RulesQueryArgs = { ...args, type: '' };

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });

      it('should fail with not existing page type', async () => {
        const modifiedArgs: RulesQueryArgs = { ...args, type: 'notExists' };

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });

      it('should fail with not matching serviceId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, serviceId: 'invalid' }
        };

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });

      it('should fail without serviceId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, serviceId: undefined }
        } as unknown as RulesQueryArgs;

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });

      it('should fail without deviceType', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, deviceType: undefined }
        } as unknown as RulesQueryArgs;

        expect(async () => await service.getPageRules(modifiedArgs)).rejects.toThrow();
      });
    });

    describe('location Info Page Type', () => {
      it('should find rules with correct locationInfoPageType', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoPageType: 'main_page' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(2);
      });

      it('should find rules with empty locationInfoPageType', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoPageType: '' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(2);
      });
    });

    describe('location Info Page Id', () => {
      it('should find rules with matching or empty locationInfoPageId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoPageId: '123' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(14);
      });

      it('should find rules with empty locationInfoPageId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoPageId: '' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(15);
      });
    });

    describe('location Info Section Id', () => {
      it('should find rules with matching or empty locationInfoSectionId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoSectionId: '123' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(14);
      });

      it('should find rules with empty locationInfoSectionId', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, locationInfoSectionId: '' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(15);
      });
    });

    describe('siteVersion', () => {
      it('should find rules with matching or empty siteVersion', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, siteVersion: 'nonExistent' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(11);
      });

      it('should find rules with empty siteVersion', async () => {
        const modifiedArgs: RulesQueryArgs = {
          ...args,
          contentMeta: { ...contentMeta, siteVersion: '' }
        };

        const response = await service.getPageRules(modifiedArgs);
        expect(response).toHaveLength(11);
      });
    });
  });
});
